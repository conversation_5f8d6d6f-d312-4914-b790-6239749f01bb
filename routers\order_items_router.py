from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func
from database.database_connection import get_db
from database.database_model import It<PERSON><PERSON><PERSON><PERSON>eader, ItemSalesDetail
from typing import Dict, Optional
from shared_state import search_history
from datetime import datetime, timedelta
from fastapi.responses import FileResponse
import pandas as pd
from io import BytesIO
import os
from datetime import datetime
from tempfile import NamedTemporaryFile
import os

router = APIRouter()
templates = Jinja2Templates(directory="templates")

def format_price_in_thousands(value):
    if value is None:
        return "0"
    return f"{float(value) / 1000:.1f}".rstrip('0').rstrip('.')

templates.env.filters["thousands"] = format_price_in_thousands

@router.get("/order-items/{search_key}", response_class=HTMLResponse)
async def order_items(
    request: Request,
    search_key: str,
    db: Session = Depends(get_db)
):
    if search_key not in search_history:
        raise HTTPException(status_code=404, detail="Search results not found")
    
    df, search_term = search_history[search_key]
    
    # Get current date for calculations
    current_date = datetime.now()
    one_year_ago = current_date - timedelta(days=365)
    
    # Calculate monthly sales for each item
    monthly_sales = {}
    for kodeitem in df['kodeitem'].unique():
        monthly_data = []
        has_sales = False
        
        # Get current month
        current_month = current_date.month
        
        for i in range(3):
            # Calculate month and year for last year's corresponding months
            month = (current_month + i) % 12 or 12  # Convert 0 to 12
            year = current_date.year - 1
            
            # Create start and end dates for the month
            start_date = datetime(year, month, 1)
            if month == 12:
                end_date = datetime(year + 1, 1, 1)
            else:
                end_date = datetime(year, month + 1, 1)
            
            month_sales = db.query(func.sum(ItemSalesDetail.jumlah))\
                .join(
                    ItemSalesHeader,
                    (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                    (ItemSalesHeader.tipe == 'KSR')
                ).filter(
                    ItemSalesDetail.kodeitem == kodeitem,
                    ItemSalesHeader.tanggal >= start_date,
                    ItemSalesHeader.tanggal < end_date
                ).scalar() or 0
            
            if month_sales > 0:
                has_sales = True
            monthly_data.append(str(int(month_sales)))
        
        if has_sales:
            monthly_sales[kodeitem] = '-'.join(monthly_data)  # Will show Mar-Apr-May
        else:
            monthly_sales[kodeitem] = ''
    
    # Calculate yearly sales and stock needed for 3 months
    yearly_sales = db.query(
        ItemSalesDetail.kodeitem,
        func.sum(ItemSalesDetail.jumlah).label('total_sales')
    ).join(
        ItemSalesHeader,
        (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
        (ItemSalesHeader.tipe == 'KSR')
    ).filter(
        ItemSalesHeader.tanggal >= one_year_ago,
        ItemSalesDetail.kodeitem.in_(df['kodeitem'].tolist())
    ).group_by(
        ItemSalesDetail.kodeitem
    ).all()
    
    sales_dict = {item.kodeitem: item.total_sales or 0 for item in yearly_sales}
    
    # Process DataFrame to keep only base units and add new columns
    distinct_items = {}
    
    for _, row in df.iterrows():
        kodeitem = row['kodeitem']
        
        if kodeitem not in distinct_items or row['tipe'] == 'D':
            item_dict = row.to_dict()
            
            # Add last year 3 months sales
            item_dict['last_year_3months_sales'] = monthly_sales.get(kodeitem, '')
            
            # Calculate stock needed for 3 months
            yearly_sale = sales_dict.get(kodeitem, 0)
            three_month_need = (yearly_sale * 3) / 12
            current_stock = row['stok']
            needed = three_month_need - current_stock if current_stock is not None else three_month_need
            item_dict['stok_needed_3month'] = round(needed, 2) if needed > 0 else None
            
            distinct_items[kodeitem] = item_dict
            
            # Update the DataFrame with the calculated values
            df.loc[df['kodeitem'] == kodeitem, 'last_year_3months_sales'] = monthly_sales.get(kodeitem, '')
            df.loc[df['kodeitem'] == kodeitem, 'stok_needed_3month'] = round(needed, 2) if needed > 0 else None
    
    # Update the search_history with the modified DataFrame
    search_history[search_key] = (df, search_term)
    
    # Convert back to list of dictionaries for template
    items = list(distinct_items.values())
    
    return templates.TemplateResponse(
        "order_items.html",
        {
            "request": request,
            "items": items,
            "search_term": search_term
        }
    )

@router.get("/export-order-items/{search_key}")
async def export_order_items(
    search_key: str,
    buy_values: Optional[str] = None,
    db: Session = Depends(get_db)
):
    if search_key not in search_history:
        raise HTTPException(status_code=404, detail="Search results not found")
    
    df, search_term = search_history[search_key]
    
    # Filter DataFrame to keep only base units (tipe='D')
    df = df[df['tipe'] == 'D']
    
    # Parse buy_values if provided
    buy_dict = {}
    if buy_values:
        try:
            from urllib.parse import unquote
            import json
            buy_dict = json.loads(unquote(buy_values))
        except:
            pass
    
    # Create a temporary file
    with NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp:
        # Create timestamp for filename
        timestamp = datetime.now().strftime("%d.%m.%Y-%H.%M.%S")
        filename = f"{search_term}-{timestamp}.xlsx"
        
        # Export data to Excel
        export_data = []
        for item in df.to_dict('records'):
            kodeitem = item['kodeitem']
            export_data.append({
                'Kode Item': kodeitem,
                'Nama Item': item['namaitem'],
                'Stok': item['stok'],
                'Stok Needed 3M': item.get('stok_needed_3month', ''),
                'Last 3M Sales': item.get('last_year_3months_sales', ''),
                'Satuan': item['satuan'],
                'Merek': item['merek'],
                'Jenis': item['jenis'],
                'Harga Pokok': item['hargapokok'],
                'Harga Jual': item['hargajual'],
                'Rak': item['rak'],
                'Buy': buy_dict.get(kodeitem, '')  # Get buy value from dictionary
            })
        
        export_df = pd.DataFrame(export_data)
        export_df.to_excel(tmp.name, index=False)
        
        # Return the file
        response = FileResponse(
            tmp.name,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
        # Delete the temporary file after sending
        response.background = lambda: os.unlink(tmp.name)
        
        return response
