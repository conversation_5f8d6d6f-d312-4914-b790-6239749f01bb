<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Monthly Low Stock Items</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };
    </script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Include today-sales-details.js with defer to ensure it loads properly -->
    <script src="/static/js/today-sales-details.js" defer></script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen">
      <!-- Navbar - Made sticky -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center space-x-4">
              <h1 class="text-3xl font-bold text-white">
                Monthly Low Stock Items
              </h1>
              <!-- Month Selector -->
              <div
                class="flex items-center space-x-2 bg-gray-700 rounded-lg px-4 py-2"
              >
                <a
                  href="/monthly-low-stock-items?month={{ prev_month }}&year={{ prev_year }}"
                  class="text-gray-300 hover:text-white transition-colors duration-150"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
                <span class="text-xl">{{ month_name }} {{ current_year }}</span>
                <a
                  href="/monthly-low-stock-items?month={{ next_month }}&year={{ next_year }}"
                  class="text-gray-300 hover:text-white transition-colors duration-150"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
              </div>
            </div>
            <!-- Close Button -->
            <button
              onclick="window.close()"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
              Close
            </button>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-[95%] mx-auto px-4 py-6">
        <!-- Items count info -->
        <div class="mb-4">
          <span class="text-lg"
            >Items Sold in Selected Month with Low Stock:
          </span>
          <span class="text-lg font-bold text-yellow-400"
            >{{ items|length }}</span
          >
        </div>

        <!-- Table -->
        <div class="bg-gray-800 rounded-lg p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-700">
                <tr>
                  <th class="px-4 py-3 text-left">Code</th>
                  <th class="px-4 py-3 text-left">Name</th>
                  <th class="px-4 py-3 text-right">Price</th>
                  <th class="px-4 py-3 text-right">Total Sold</th>
                  <th class="px-4 py-3 text-left">Details</th>
                </tr>
              </thead>
              <tbody class="bg-black divide-y divide-gray-700">
                {% for item in items %}
                <tr class="hover:bg-gray-700 transition duration-150">
                  <td
                    class="px-3 py-1 whitespace-nowrap text-base text-gray-300"
                  >
                    <a
                      href="/item-history/{{ item.code }}"
                      target="_blank"
                      class="hover:text-blue-400 transition-colors duration-150"
                      >{{ item.code }}</a
                    >
                  </td>
                  <td class="px-3 py-1 whitespace-nowrap text-base">
                    {% set parts = item.name.split(' - ', 2) %}
                    <span
                      class="text-yellow-400 cursor-pointer search-item-name"
                      data-item-name="{{ parts[1] }}"
                      data-item-code="{{ item.code }}"
                      >{{ parts[0] }}</span
                    >
                    <span class="text-gray-300"> - </span>
                    <span
                      class="text-gray-300 cursor-pointer search-item-name"
                      data-item-name="{{ parts[1] }}"
                      data-item-code="{{ item.code }}"
                      >{{ parts[1] }}</span
                    >
                    <span class="text-gray-300"> - </span>
                    <span class="text-red-400">{{ parts[2] }}</span>
                    <button
                      class="ml-1 text-gray-400 hover:text-white copy-button"
                      data-copy-text="{{ parts[1] }}"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                        />
                      </svg>
                    </button>
                  </td>
                  <td class="px-4 py-3 text-right">
                    {{ item.price | currency }}
                  </td>
                  <td class="px-4 py-3 text-right">{{ item.total_sold }}</td>
                  <td class="px-4 py-3">{{ item.details }}</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Add copy on select functionality
      document.addEventListener("mouseup", function () {
        const selectedText = window.getSelection().toString().trim();
        if (selectedText) {
          navigator.clipboard.writeText(selectedText).catch((err) => {
            console.error("Failed to copy text: ", err);
          });
        }
      });

      // Initialize the chat history event listeners when the DOM is loaded
      document.addEventListener("DOMContentLoaded", function () {
        if (typeof setupChatHistoryEventListeners === "function") {
          setupChatHistoryEventListeners();
        } else {
          console.error("setupChatHistoryEventListeners function not found");
        }
      });
    </script>
  </body>
</html>
