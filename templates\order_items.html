<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Order Items - Toko LARIS LAX</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };

      function validateNumber(input) {
        // Only allow positive numbers
        input.value = input.value.replace(/[^0-9]/g, "");
      }

      let buyValues = {};

      function setupInputNavigation() {
        const buyInputs = document.querySelectorAll("input[data-kodeitem]");

        buyInputs.forEach((input, index) => {
          // Load any previously stored value
          const kodeitem = input.getAttribute("data-kodeitem");
          if (localStorage.getItem(`buy_${kodeitem}`)) {
            input.value = localStorage.getItem(`buy_${kodeitem}`);
            buyValues[kodeitem] = input.value;
          }

          input.addEventListener("input", function () {
            const kodeitem = this.getAttribute("data-kodeitem");
            buyValues[kodeitem] = this.value;
            localStorage.setItem(`buy_${kodeitem}`, this.value);
          });

          input.addEventListener("keydown", function (e) {
            if (e.key === "Enter" || e.key === "ArrowDown") {
              e.preventDefault();
              // Get next input in the list
              const nextInput = buyInputs[index + 1];
              if (nextInput) {
                nextInput.focus();
                nextInput.select();
              }
            } else if (e.key === "ArrowUp") {
              e.preventDefault();
              // Get previous input in the list
              const prevInput = buyInputs[index - 1];
              if (prevInput) {
                prevInput.focus();
                prevInput.select();
              }
            }
          });
        });
      }
      // Add this after tailwind config
      let sortDirection = {
        kodeitem: "asc",
        namaitem: "asc",
        merek: "asc",
        jenis: "asc",
        rak: "asc",
      };

      function sortTable(column) {
        const table = document.querySelector("table");
        const tbody = table.querySelector("tbody");
        const rows = Array.from(tbody.querySelectorAll("tr"));

        sortDirection[column] =
          sortDirection[column] === "asc" ? "desc" : "asc";

        rows.sort((a, b) => {
          let aValue, bValue;

          const columnMap = {
            kodeitem: 1,
            namaitem: 2,
            merek: 6,
            jenis: 7,
            rak: 9,
          };

          aValue = a
            .querySelector(`td:nth-child(${columnMap[column]})`)
            .textContent.trim();
          bValue = b
            .querySelector(`td:nth-child(${columnMap[column]})`)
            .textContent.trim();

          if (sortDirection[column] === "asc") {
            return aValue.localeCompare(bValue);
          } else {
            return bValue.localeCompare(aValue);
          }
        });

        rows.forEach((row) => tbody.appendChild(row));
      }
      document.addEventListener("DOMContentLoaded", setupInputNavigation);
    </script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen">
      <!-- Navbar -->
      <nav class="bg-gray-800 shadow-lg">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <h1 class="text-3xl font-bold text-white">Order Items</h1>
            </div>
            <!-- Navigation controls -->
            <div class="flex items-center space-x-4">
              <span class="text-gray-300 mr-4">Search term: {{ search_term }}</span>
              <button
                onclick="exportToExcel()"
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition duration-150"
              >
                Export to Excel
              </button>
              <button
                onclick="window.close()"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-150 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
                Close
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-[95%] mx-auto px-4 py-4">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-700">
            <thead class="bg-gray-700">
              <tr>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-blue-400 transition-colors duration-150"
                  onclick="sortTable('kodeitem')"
                >
                  Kode Item
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-blue-400 transition-colors duration-150"
                  onclick="sortTable('namaitem')"
                >
                  Nama Item
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Stok
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Stok Needed 3M
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Last 3M Sales
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-blue-400 transition-colors duration-150"
                  onclick="clearAllBuyValues()"
                >
                  Buy
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-blue-400 transition-colors duration-150"
                  onclick="sortTable('merek')"
                >
                  Merek
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-blue-400 transition-colors duration-150"
                  onclick="sortTable('jenis')"
                >
                  Jenis
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider"
                >
                  Harga ({{ 'HPP/HJ' }})
                </th>
                <th
                  scope="col"
                  class="px-3 py-1 text-left text-base font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-blue-400 transition-colors duration-150"
                  onclick="sortTable('rak')"
                >
                  Rak
                </th>
              </tr>
            </thead>
            <tbody class="bg-gray-800 divide-y divide-gray-700">
              {% for item in items %}
              <tr class="hover:bg-gray-700 transition duration-150">
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  <a
                    href="/item-history/{{ item.kodeitem }}"
                    target="_blank"
                    class="hover:text-blue-400 transition-colors duration-150"
                    >{{ item.kodeitem }}</a
                  >
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  {{ item.namaitem }}
                </td>
                <td
                  class="px-3 py-1 whitespace-nowrap text-base {% if item.stok <= item.stokmin %}text-red-400{% else %}text-gray-300{% endif %}"
                >
                  {{ "%.2f"|format(item.stok) }} {{ item.satuan }}
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  {% if item.stok_needed_3month %} {{
                  "%.2f"|format(item.stok_needed_3month) }} {{ item.satuan }} {%
                  endif %}
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  {{ item.last_year_3months_sales }}
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  <input
                    type="text"
                    class="w-20 px-2 py-1 text-base rounded border border-gray-600 bg-black text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    oninput="validateNumber(this)"
                    data-kodeitem="{{ item.kodeitem }}"
                    data-satuan="{{ item.satuan }}"
                  />
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  {{ item.merek }}
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  {{ item.jenis }}
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  {{ item.hargapokok|thousands }} / {{ item.hargajual|thousands
                  }}
                </td>
                <td class="px-3 py-1 whitespace-nowrap text-base text-gray-300">
                  {{ item.rak }}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>
</html>

<script>
  function exportToExcel() {
    const searchKey = window.location.pathname.split("/").pop();
    const buyValuesParam = encodeURIComponent(JSON.stringify(buyValues));
    window.location.href = `/export-order-items/${searchKey}?buy_values=${buyValuesParam}`;
  }

  function clearAllBuyValues() {
    // Clear all input values
    const buyInputs = document.querySelectorAll("input[data-kodeitem]");
    buyInputs.forEach((input) => {
      input.value = "";
      const kodeitem = input.getAttribute("data-kodeitem");
      // Clear from localStorage and buyValues object
      localStorage.removeItem(`buy_${kodeitem}`);
      delete buyValues[kodeitem];
    });
  }
</script>
