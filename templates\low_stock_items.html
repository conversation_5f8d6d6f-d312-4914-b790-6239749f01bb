<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Today Low Stock Items</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };
    </script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  </head>
  <body class="bg-black text-gray-100">
    <div class="min-h-screen">
      <!-- Navbar -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between h-16">
            <div class="flex items-center space-x-4">
              <h1 class="text-3xl font-bold text-white">
                Today Low Stock Items
              </h1>
              <!-- Date Navigation -->
              <div
                class="flex items-center space-x-2 bg-gray-700 rounded-lg px-4 py-2"
              >
                <a
                  href="/low-stock-items?date={{ prev_date }}"
                  class="text-gray-300 hover:text-white transition-colors duration-150"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
                <input
                  type="date"
                  id="dateSelector"
                  value="{{ current_date }}"
                  class="bg-transparent text-xl text-center focus:outline-none"
                  onchange="window.location.href = '/low-stock-items?date=' + this.value"
                />
                <a
                  href="/low-stock-items?date={{ next_date }}"
                  class="text-gray-300 hover:text-white transition-colors duration-150"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button
                onclick="sendReport()"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center mr-2"
              >
                Send Report
              </button>
              <button
                onclick="window.close()"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-[95%] mx-auto px-4 py-6">
        <div class="bg-gray-800 rounded-lg p-6">
          <!-- Items Count -->
          <div class="mb-4">
            <span class="text-lg">Items Sold Today with Low Stock: </span>
            <span class="text-lg font-bold text-yellow-400"
              >{{ items|length }}</span
            >
          </div>
          <!-- Table -->
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-700">
                <tr>
                  <th class="px-4 py-3 text-left">Code</th>
                  <th class="px-4 py-3 text-left">Name</th>
                  <th class="px-4 py-3 text-right">Price</th>
                  <th class="px-4 py-3 text-left">Details</th>
                </tr>
              </thead>
              <tbody class="bg-black divide-y divide-gray-700">
                {% for item in items %}
                <tr
                  class="hover:bg-gray-700 transition duration-150"
                  data-sale-date="{{ item.sale_date }}"
                >
                  <td
                    class="px-3 py-1 whitespace-nowrap text-base text-gray-300"
                  >
                    <a
                      href="/item-history/{{ item.code }}"
                      target="_blank"
                      class="hover:text-blue-400 transition-colors duration-150"
                      >{{ item.code }}</a
                    >
                  </td>
                  <td class="px-3 py-1 whitespace-nowrap text-base">
                    {% set parts = item.name.split(' - ', 2) %}
                    <span
                      class="text-yellow-400 cursor-pointer item-name-part"
                      onclick="searchChatHistory('{{ parts[1] }}', '{{ item.code }}')"
                      >{{ parts[0] }}</span
                    >
                    <span class="text-gray-300"> - </span>
                    <span
                      class="text-gray-300 cursor-pointer item-name-part"
                      onclick="searchChatHistory('{{ parts[1] }}', '{{ item.code }}')"
                      >{{ parts[1] }}</span
                    >
                    <span class="text-gray-300"> - </span>
                    <span class="text-red-400">{{ parts[2] }}</span>
                    <button
                      class="ml-1 text-gray-400 hover:text-white"
                      onclick="copyToClipboard('{{ parts[1] }}'); event.stopPropagation();"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                        />
                      </svg>
                    </button>
                  </td>
                  <td class="px-4 py-3 text-right whitespace-nowrap">
                    {{ item.price | currency }}
                  </td>
                  <td class="px-4 py-3 whitespace-nowrap">
                    {{ item.details }}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script>
  // Add copy on select functionality
  document.addEventListener("mouseup", function () {
    const selectedText = window.getSelection().toString().trim();
    if (selectedText) {
      navigator.clipboard.writeText(selectedText).catch((err) => {
        console.error("Failed to copy text: ", err);
      });
    }
  });

  async function sendReport() {
    const urlParams = new URLSearchParams(window.location.search);
    const date = urlParams.get("date") || "";

    try {
      const response = await fetch(`/send-report?date=${date}`);
      const data = await response.json();

      if (data.status === "success") {
        Toastify({
          text: "Report sent successfully to Telegram",
          duration: 3000,
          gravity: "top",
          position: "right",
          style: {
            background: "#22c55e", // green-600
          },
        }).showToast();
      } else {
        Toastify({
          text: data.message,
          duration: 3000,
          gravity: "top",
          position: "right",
          style: {
            background: "#ef4444", // red-600
          },
        }).showToast();
      }
    } catch (error) {
      Toastify({
        text: "Failed to send report",
        duration: 3000,
        gravity: "top",
        position: "right",
        style: {
          background: "#ef4444", // red-600
        },
      }).showToast();
      console.error("Error:", error);
    }
  }

  function copyToClipboard(text) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        // Show a toast notification
        Toastify({
          text: "Product name copied!",
          duration: 2000,
          gravity: "bottom",
          position: "right",
          backgroundColor: "#4CAF50",
        }).showToast();
      })
      .catch((err) => {
        console.error("Failed to copy text: ", err);
      });
  }

  async function searchChatHistory(itemName, itemCode) {
    try {
      const response = await fetch(
        `/search-chat-history?item=${encodeURIComponent(itemCode)}`
      );
      if (!response.ok) {
        throw new Error("Failed to search chat history");
      }

      const data = await response.json();
      const chatHistory = data.chat_history || [];

      // Sort by date (newest first)
      chatHistory.sort((a, b) => new Date(b.date) - new Date(a.date));

      // Create popup with itemName for display purposes
      const popup = document.createElement("div");
      popup.className =
        "fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50";

      // Check for purchase history entries (transactions)
      const purchaseEntries = chatHistory.filter(
        (item) => item.user && item.user.includes("/UTM/")
      );

      // Check for chat history entries (non-transactions)
      const chatEntries = chatHistory.filter(
        (item) => !item.user || !item.user.includes("/UTM/")
      );

      // Get the newest purchase date if any
      let newestPurchaseDate = null;
      if (purchaseEntries.length > 0) {
        newestPurchaseDate = new Date(
          Math.max(...purchaseEntries.map((item) => new Date(item.date)))
        );
      }

      // Get the newest chat date if any
      let newestChatDate = null;
      if (chatEntries.length > 0) {
        newestChatDate = new Date(
          Math.max(...chatEntries.map((item) => new Date(item.date)))
        );
      }

      // Check if there's a purchase within the last 30 days
      const today = new Date();
      const hasRecentPurchase = purchaseEntries.some((item) => {
        const date = new Date(item.date);
        const diffTime = Math.abs(today - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 30;
      });

      // Check if there's a chat entry within the last 30 days
      const hasRecentChat = chatEntries.some((item) => {
        const date = new Date(item.date);
        const diffTime = Math.abs(today - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 30;
      });

      // Only play sound if:
      // 1. There are recent chat entries (less than 30 days old)
      // 2. AND chat history is newer than purchase history (if purchase history exists)
      const shouldPlaySound =
        hasRecentChat &&
        (!newestPurchaseDate ||
          !newestChatDate ||
          newestChatDate > newestPurchaseDate);

      if (shouldPlaySound) {
        const audio = new Audio("/static/sound/ding.wav");
        audio.play().catch((err) => console.error("Error playing sound:", err));
      }

      // Create content
      let content = `
            <div class="bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">Chat History for "${itemName}"</h2>
                    <button class="text-gray-400 hover:text-white" id="closePopup">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="mb-4">
                    <button id="sendToTelegram" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                        Send to Telegram
                    </button>
                </div>`;

      // Chat History Section
      content += `
            <div>
                <h3 class="text-lg font-semibold mb-2 text-blue-400">Item History</h3>`;

      if (chatHistory.length === 0) {
        content += `
                <div class="flex flex-col items-center justify-center py-4">
                    <img src="/static/img/none.jpg" alt="No history found" class="max-w-full h-auto rounded-lg mb-2">
                    <p class="text-gray-300">No history found for this item.</p>
                </div>`;
      } else {
        content += `
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-4 py-2 text-left">Date</th>
                            <th class="px-4 py-2 text-left">Source</th>
                            <th class="px-4 py-2 text-left">Details</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700">`;

        chatHistory.forEach((item) => {
          // Format date to DD-MM-YYYY
          const date = new Date(item.date);
          const formattedDate = `${date
            .getDate()
            .toString()
            .padStart(2, "0")}-${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}-${date.getFullYear()}`;

          // Calculate days difference
          const today = new Date();
          const diffTime = Math.abs(today - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          // Determine message color based on age
          let messageClass = "text-white"; // Default color
          if (diffDays > 60) {
            messageClass = "text-red-400"; // Red for > 60 days
          } else if (diffDays > 30) {
            messageClass = "text-yellow-400"; // Yellow for > 30 days
          }

          // Check if this is a transaction entry (from purchase history)
          const isTransaction = item.user && item.user.includes("/UTM/");
          const strikeClass = isTransaction ? "line-through" : "";

          content += `
                    <tr class="hover:bg-gray-700">
                        <td class="px-4 py-2 ${messageClass} ${strikeClass}">${formattedDate}</td>
                        <td class="px-4 py-2 ${messageClass} ${strikeClass}">${item.user}</td>
                        <td class="px-4 py-2 ${messageClass} ${strikeClass}">${item.message}</td>
                    </tr>`;
        });

        content += `
                    </tbody>
                </table>`;
      }
      content += `</div>`;

      content += `</div>`;
      popup.innerHTML = content;

      // Add to body
      document.body.appendChild(popup);

      // Add close functionality
      document.getElementById("closePopup").addEventListener("click", () => {
        document.body.removeChild(popup);
      });

      // Add send to Telegram functionality
      document
        .getElementById("sendToTelegram")
        .addEventListener("click", async () => {
          try {
            const sendResponse = await fetch("/send-to-telegram", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                message: itemName,
                user_id: "YourTelegramUsername", // Replace with your Telegram username or ID
              }),
            });

            const result = await sendResponse.json();

            if (result.success) {
              Toastify({
                text: "Message sent to Telegram",
                duration: 3000,
                gravity: "top",
                position: "right",
                style: {
                  background: "#22c55e", // green-600
                },
              }).showToast();

              // Close the popup after successful send
              document.body.removeChild(popup);
            } else {
              throw new Error(result.error || "Failed to send message");
            }
          } catch (error) {
            console.error("Error sending to Telegram:", error);
            Toastify({
              text: `Failed to send message: ${error.message}`,
              duration: 3000,
              gravity: "top",
              position: "right",
              style: {
                background: "#ef4444", // red-600
              },
            }).showToast();
          }
        });

      // Close on click outside
      popup.addEventListener("click", (e) => {
        if (e.target === popup) {
          document.body.removeChild(popup);
        }
      });
    } catch (error) {
      console.error("Error searching chat history:", error);
      Toastify({
        text: "Failed to search chat history",
        duration: 3000,
        gravity: "top",
        position: "right",
        style: {
          background: "#ef4444", // red-600
        },
      }).showToast();
    }
  }

  // Apply color coding based on item age
  function applyItemAgeColors() {
    const rows = document.querySelectorAll("tbody tr[data-sale-date]");
    const today = new Date();

    rows.forEach((row) => {
      const saleDate = row.getAttribute("data-sale-date");
      if (!saleDate) return;

      const itemDate = new Date(saleDate);
      const diffTime = Math.abs(today - itemDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Get the item name parts (first two spans with item-name-part class)
      const itemNameParts = row.querySelectorAll(".item-name-part");

      // Determine color based on age
      let colorClass = "text-white"; // Default color for recent items (≤30 days)
      if (diffDays > 60) {
        colorClass = "text-red-400"; // Red for > 60 days
      } else if (diffDays > 30) {
        colorClass = "text-yellow-400"; // Yellow for > 30 days
      }

      // Apply color to item name parts
      itemNameParts.forEach((part) => {
        // Remove existing color classes
        part.classList.remove(
          "text-white",
          "text-yellow-400",
          "text-red-400",
          "text-gray-300"
        );
        // Add new color class
        part.classList.add(colorClass);
      });
    });
  }

  // Apply colors when page loads
  document.addEventListener("DOMContentLoaded", applyItemAgeColors);
</script>
