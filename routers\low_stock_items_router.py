from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import aiohttp
from datetime import datetime, timedelta
from database.database_connection import get_db
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from database.database_model import (
    Item,
    ItemSalesHeader,
    ItemSalesDetail,
    ItemStock,
    ItemSellingPrice,
    ItemPurchaseHeader,
    ItemPurchaseDetail
)
import json
import os
from telethon import TelegramClient

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Add currency filter
def currency_filter(value):
    try:
        return f"Rp {int(value):,}"
    except (ValueError, TypeError):
        return "Rp 0"

# Register the currency filter
templates.env.filters["currency"] = currency_filter

@router.get("/low-stock-items", response_class=HTMLResponse)
async def low_stock_items(request: Request, date: str = None, db: Session = Depends(get_db)):
    if not date:
        date = datetime.now().strftime("%Y-%m-%d")
    
    current_date = datetime.strptime(date, "%Y-%m-%d")
    prev_date = (current_date - timedelta(days=1)).strftime("%Y-%m-%d")
    next_date = (current_date + timedelta(days=1)).strftime("%Y-%m-%d")

    # Query to get items with low stock that were sold on the specified date
    query = db.query(
        Item.kodeitem.label('code'),
        Item.namaitem.label('name'),
        ItemSellingPrice.hargajual.label('price'),
        ItemStock.stok,
        Item.stokmin.label('stokmin'),
        Item.keterangan.label('detail'),
        ItemSalesHeader.tanggal  # Add this column to the select list
    ).join(
        ItemSalesDetail,
        Item.kodeitem == ItemSalesDetail.kodeitem
    ).join(
        ItemSalesHeader,
        and_(
            ItemSalesDetail.notransaksi == ItemSalesHeader.notransaksi,
            func.date(ItemSalesHeader.tanggal) == current_date.date(),
            ItemSalesHeader.tipe == 'KSR'  # Only include actual sales
        )
    ).join(
        ItemStock,
        Item.kodeitem == ItemStock.kodeitem
    ).outerjoin(
        ItemSellingPrice,
        and_(
            Item.kodeitem == ItemSellingPrice.kodeitem,
            ItemSellingPrice.satuan == Item.satuan  # Match on base unit
        )
    ).filter(
        ItemStock.stok <= Item.stokmin
    ).distinct().order_by(ItemSalesHeader.tanggal.desc())

    results = query.all()
    
    items = [
        {
            'code': result.code,
            'name': format_stock_and_name(result.stok, result.name, result.stokmin),
            'price': result.price,
            'details': result.detail if result.detail else ''  # Keep this as detail since we labeled it as detail
        }
        for result in results
    ]

    return templates.TemplateResponse(
        "low_stock_items.html",
        {
            "request": request,
            "items": items,
            "current_date": date,
            "prev_date": prev_date,
            "next_date": next_date
        }
    )

def format_number(value):
    """Format number to remove decimal if it's a whole number"""
    if value is None:
        return "0"
    return f"{value:.2f}".rstrip('0').rstrip('.')

def format_stock_and_name(stock, name, stokmin):
    """Format stock, name, and minimum stock together, removing decimal if it's a whole number"""
    formatted_stock = format_number(stock)
    formatted_stokmin = format_number(stokmin if stokmin is not None else 0)
    return f"{formatted_stock} - {name} - {formatted_stokmin}"

# Telegram API credentials
API_ID = '27446563'
API_HASH = '7eb34408a523b57d51569fef68421249'
BOT_TOKEN = '7288942301:AAEzgqvaMZVirnp2g4-W8GTFWEDkfpHvayM'
BARANG_KURANG_GROUP_ID = -4597303816  # Using your original TELEGRAM_CHAT_ID

# Use a unique session name to avoid conflicts
# session_name = f'session_name_{os.getpid()}'

# Initialize client with a unique session name
client = None

# Initialize client lazily to avoid database lock issues
async def get_telegram_client():
    global client
    if client is None:
        client = TelegramClient('session_name', API_ID, API_HASH)
    return client

async def send_telegram_message(message: str):
    # Get the client
    telegram_client = await get_telegram_client()
    
    # Use async with to properly handle the client session
    async with telegram_client:
        try:
            # Note: We're not using bot_token here, so it will authenticate as a user
            await telegram_client.send_message(BARANG_KURANG_GROUP_ID, message, parse_mode='html')
            return {"success": True}
        except Exception as e:
            error_detail = str(e)
            print(f"Telegram API Error: {error_detail}")
            raise HTTPException(status_code=500, detail=f"Failed to send Telegram message: {error_detail}")

@router.get("/send-report")
async def send_report(request: Request, date: str = None, db: Session = Depends(get_db)):
    try:
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")
        
        current_date = datetime.strptime(date, "%Y-%m-%d")

        # Query to get items with low stock that were sold on the specified date
        query = db.query(
            Item.namaitem,
            ItemStock.stok,
            Item.stokmin
        ).join(
            ItemSalesDetail,
            Item.kodeitem == ItemSalesDetail.kodeitem
        ).join(
            ItemSalesHeader,
            and_(
                ItemSalesDetail.notransaksi == ItemSalesHeader.notransaksi,
                func.date(ItemSalesHeader.tanggal) == current_date.date(),
                ItemSalesHeader.tipe == 'KSR'  # Only include actual sales
            )
        ).join(
            ItemStock,
            Item.kodeitem == ItemStock.kodeitem
        ).filter(
            ItemStock.stok <= Item.stokmin
        ).distinct().order_by(Item.namaitem.asc())

        results = query.all()

        if not results:
            return {"status": "warning", "message": "No low stock items found for the selected date"}

        # Format the message
        message_parts = ["<b>🚨 Low Stock Items Report</b>\n"]
        message_parts.append(f"<i>Date: {date}</i>\n")
        message_parts.append("<pre>")
        
        # Calculate the maximum length for proper alignment
        max_name_length = max(len(result.namaitem) for result in results)
        
        for result in results:
            padded_name = result.namaitem.ljust(max_name_length)
            formatted_stock = format_number(result.stok)
            message_parts.append(f"{padded_name}: {formatted_stock}")
        
        message_parts.append("</pre>")
        message = "\n".join(message_parts)

        try:
            await send_telegram_message(message)
            return {"status": "success", "message": "Report sent successfully to Telegram"}
        except Exception as e:
            print(f"Telegram Error: {str(e)}")
            return {"status": "error", "message": f"Failed to send Telegram message: {str(e)}"}
            
    except Exception as e:
        print(f"General Error: {str(e)}")
        return {"status": "error", "message": f"An error occurred: {str(e)}"}

@router.get("/search-chat-history")
async def search_chat_history(item: str, db: Session = Depends(get_db)):
    try:
        # Get chat history
        chat_history_path = r"H:\Python\Telegram Check Pesan Barang\chat_history.json"
        
        chat_results = []
        
        # First, get the item details to extract the name
        item_details = db.query(Item).filter(Item.kodeitem == item).first()
        
        if not item_details:
            return {"chat_history": [], "purchase_history": []}
        
        # Extract the search term from the item name
        search_term = None
        if item_details.namaitem:
            # Split by ' - ' to get the main part
            name_parts = item_details.namaitem.split(' - ')
            
            if len(name_parts) > 1:
                search_term = name_parts[1].strip()
            else:
                search_term = name_parts[0].strip()
        
        # Normalize spaces (replace multiple spaces with single space)
        if search_term:
            import re
            search_term = re.sub(r'\s+', ' ', search_term).strip()
        
        # Convert to lowercase for case-insensitive search
        search_term_lower = search_term.lower() if search_term else ""
        
        # Also create alternative search term by removing spaces
        alt_search_term = search_term_lower.replace(" ", "")
        
        # Search in chat history JSON
        if os.path.exists(chat_history_path) and search_term:
            with open(chat_history_path, 'r', encoding='utf-8') as f:
                chat_data = json.load(f)
            
            # Search for the term in messages (case-insensitive)
            for entry in chat_data:
                message = entry.get('message', '').lower()
                # Try both with normal spaces and without spaces
                if search_term_lower in message or alt_search_term in message.replace(" ", ""):
                    chat_results.append({
                        'date': entry.get('date', ''),
                        'user': entry.get('user', ''),
                        'message': entry.get('message', '')
                    })
        
        # Get purchase history for the last 60 days using kodeitem
        sixty_days_ago = datetime.now() - timedelta(days=60)
        
        # Query purchase history using the kodeitem directly
        purchase_results = db.query(
            ItemPurchaseHeader.tanggal,
            ItemPurchaseHeader.notransaksi,
            ItemPurchaseDetail.jumlah,
            ItemPurchaseDetail.satuan,
            ItemPurchaseDetail.harga
        ).join(
            ItemPurchaseDetail,
            ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
        ).filter(
            ItemPurchaseDetail.kodeitem == item,
            ItemPurchaseHeader.tanggal >= sixty_days_ago,
            ItemPurchaseHeader.tipe.in_(['BL', 'IM'])
        ).order_by(ItemPurchaseHeader.tanggal.desc()).all()
        
        # Format the purchase history as chat entries
        for purchase in purchase_results:
            # Format quantity and unit for the message column
            quantity = float(purchase.jumlah)
            quantity_str = f"{quantity:.0f}" if quantity.is_integer() else f"{quantity}"
            message = f"{quantity_str} {purchase.satuan} - Rp {float(purchase.harga):,.0f}"
            
            chat_results.append({
                'date': purchase.tanggal.isoformat(),
                'user': f"{purchase.notransaksi}",  # Transaction number in User column
                'message': message  # Quantity and unit in Message column
            })
        
        # Sort all results by date (newest first)
        chat_results.sort(key=lambda x: x['date'], reverse=True)
        
        # Format the purchase history for backward compatibility
        purchase_history = [
            {
                'date': purchase.tanggal.strftime('%Y-%m-%d'),
                'notransaksi': purchase.notransaksi,
                'jumlah': float(purchase.jumlah),
                'satuan': purchase.satuan,
                'harga': float(purchase.harga)
            }
            for purchase in purchase_results
        ]
                
        return {
            "chat_history": chat_results,
            "purchase_history": purchase_history
        }
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {"chat_history": [], "purchase_history": []}

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import aiohttp
import json
import os
from datetime import datetime
import asyncio

# Telegram API credentials
API_ID = '27446563'
API_HASH = '7eb34408a523b57d51569fef68421249'
BOT_TOKEN = '7288942301:AAEzgqvaMZVirnp2g4-W8GTFWEDkfpHvayM'
GROUP_ID = -541029745

# Use a unique session name to avoid conflicts
# session_name = f'session_name_{os.getpid()}'

# Initialize client with a unique session name
client = None

# Initialize client lazily to avoid database lock issues
async def get_telegram_client():
    global client
    if client is None:
        client = TelegramClient('session_name', API_ID, API_HASH)
    return client

class TelegramMessage(BaseModel):
    message: str
    user_id: str = "Seiya44-Web"  # Default value if not provided

@router.post("/send-to-telegram")
async def send_to_telegram(message_data: TelegramMessage):
    try:
        # Get the client
        telegram_client = await get_telegram_client()
        
        # Use async with to properly handle the client session
        async with telegram_client:
            # Send message using Telethon as a user (not using bot_token)
            await telegram_client.send_message(GROUP_ID, message_data.message)
            
            # Also add to chat history
            chat_history_path = r"H:\Python\Telegram Check Pesan Barang\chat_history.json"
            
            if os.path.exists(chat_history_path):
                with open(chat_history_path, 'r', encoding='utf-8') as f:
                    chat_data = json.load(f)
            else:
                chat_data = []
            
            # Add new entry
            chat_data.insert(0, {
                "date": datetime.now().isoformat(),
                "user": message_data.user_id,  # Use the provided user_id
                "message": message_data.message
            })
            
            # Save updated chat history
            with open(chat_history_path, 'w', encoding='utf-8') as f:
                json.dump(chat_data, f, ensure_ascii=False, indent=2)
            
            return {"success": True}
                
    except Exception as e:
        print(f"Error sending to Telegram: {str(e)}")
        return {"success": False, "error": str(e)}





