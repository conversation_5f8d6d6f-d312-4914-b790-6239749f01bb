from fastapi import APIRouter, Depends, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func, and_  # Add and_ to the imports
from datetime import datetime
from database.database_connection import get_db
from database.database_model import (
    Item,
    ItemUnitConversion,
    ItemStock,
    ItemSalesHeader,
    ItemSalesDetail
)
import pytz
from decimal import Decimal

router = APIRouter()
templates = Jinja2Templates(directory="templates")

def calculate_profit(total, hargapokok, jumlah):
    if hargapokok is None:
        return 0
    # Convert values to Decimal for precise calculation
    try:
        total = Decimal(str(total))
        hargapokok = Decimal(str(hargapokok))
        jumlah = Decimal(str(jumlah))
        modal = hargapokok * jumlah
        return total - modal
    except:
        return 0

@router.get("/today-sales")
async def get_today_sales(db: Session = Depends(get_db)):
    # Get today's date in Asia/Jakarta timezone
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    today = datetime.now(jakarta_tz).date()
    
    # Calculate total sales for today
    total_sales = db.query(func.sum(ItemSalesHeader.totalakhir))\
        .filter(
            func.date(ItemSalesHeader.tanggal) == today,
            ItemSalesHeader.tipe == 'KSR'
        ).scalar() or 0
    
    return {"total_sales": float(total_sales)}

@router.get("/today-sales-details", response_class=HTMLResponse)
async def get_today_sales_details(request: Request, date: str = None, db: Session = Depends(get_db)):
    # Get today's date in Asia/Jakarta timezone
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    
    if date:
        # Parse the date from the query parameter
        selected_date = datetime.strptime(date, '%Y-%m-%d').date()
    else:
        # Use today's date if no date parameter
        selected_date = datetime.now(jakarta_tz).date()
    
    # Query to get sales details for the selected date
    sales_details = db.query(
        ItemSalesHeader.notransaksi,
        ItemSalesHeader.tanggal,
        ItemSalesHeader.potfaktur,
        ItemSalesHeader.totalitem,  # Add this to get total items
        ItemSalesDetail.kodeitem,
        Item.namaitem,
        Item.stokmin,
        ItemStock.stok,
        ItemSalesDetail.harga,
        ItemSalesDetail.total,
        ItemSalesDetail.potongan,
        ItemSalesDetail.jumlah,
        ItemSalesDetail.satuan,
        ItemUnitConversion.jumlahkonv,
        ItemUnitConversion.hargapokok,
        func.to_char(ItemSalesHeader.tanggal, 'HH24:MI').label('jam')
    ).join(
        ItemSalesDetail, ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
    ).join(
        Item, ItemSalesDetail.kodeitem == Item.kodeitem
    ).outerjoin(
        ItemStock, ItemSalesDetail.kodeitem == ItemStock.kodeitem
    ).outerjoin(
        ItemUnitConversion, and_(
            ItemSalesDetail.kodeitem == ItemUnitConversion.kodeitem,
            ItemSalesDetail.satuan == ItemUnitConversion.satuan
        )
    ).filter(
        func.date(ItemSalesHeader.tanggal) == selected_date,
        ItemSalesHeader.tipe == 'KSR'
    ).order_by(
        ItemSalesHeader.tanggal.desc()
    ).all()

    # Process the results
    sales_data = []
    for sale in sales_details:
        # Calculate base unit quantity
        base_quantity = sale.jumlah
        if sale.jumlahkonv:
            base_quantity = sale.jumlah * sale.jumlahkonv

        profit = calculate_profit(sale.total, sale.hargapokok, sale.jumlah)
        
        # Get the non-zero discount (either global or per-item)
        item_discount = float(sale.potongan or 0)
        global_discount = float(sale.potfaktur or 0)
        
        # Use the full global discount value instead of dividing
        display_discount = (
            global_discount if item_discount == 0 
            else item_discount
        )

        sales_data.append({
            'notransaksi': sale.notransaksi,
            'tanggal': sale.tanggal,
            'kodeitem': sale.kodeitem,
            'namaitem': sale.namaitem,
            'stok': sale.stok if sale.stok is not None else 0,
            'stokmin': sale.stokmin,
            'harga': sale.harga,
            'total': sale.total,
            'discount': display_discount,
            'jumlah': base_quantity,
            'profit': profit,
            'jam': sale.jam
        })

    # Calculate total sales using the same method as landing page
    total_sales_from_header = db.query(func.sum(ItemSalesHeader.totalakhir))\
        .filter(
            func.date(ItemSalesHeader.tanggal) == selected_date,
            ItemSalesHeader.tipe == 'KSR'
        ).scalar() or 0
    
    # Use the header-based total instead of summing details
    total_sales = float(total_sales_from_header)
    total_profit =sum(float(sale['profit']) for sale in sales_data)
    
    return templates.TemplateResponse(
        "today_sales_details.html",
        {
            "request": request,
            "sales": sales_data,
            "total_sales": total_sales,
            "total_profit": total_profit
        }
    )
