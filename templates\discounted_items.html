<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Discounted Items - {{ month_name }} {{ current_year }}</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            backgroundColor: {
              "gray-750": "#2d3748",
              "gray-850": "#1a1a1a",
              "gray-880": "#242424",
              "gray-900": "#1e293b",
            },
          },
        },
      };
    </script>
  </head>
  <body class="bg-gray-850 text-gray-300">
    <div class="min-h-screen">
      <!-- Header - Made sticky -->
      <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
        <div class="max-w-[95%] mx-auto px-4">
          <div class="flex justify-between items-center h-16">
            <div class="flex items-center space-x-4">
              <h1 class="text-2xl font-bold">Discounted Items</h1>
              <div class="flex items-center space-x-4">
                <a
                  href="/discounted-items?month={{ prev_month }}&year={{ prev_year }}"
                  class="px-4 py-2 bg-gray-700 rounded-lg hover:bg-gray-600"
                >
                  ←
                </a>
                <span class="text-xl">{{ month_name }} {{ current_year }}</span>
                <a
                  href="/discounted-items?month={{ next_month }}&year={{ next_year }}"
                  class="px-4 py-2 bg-gray-700 rounded-lg hover:bg-gray-600"
                >
                  →
                </a>
              </div>
            </div>
            <button
              onclick="window.close()"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
              Close
            </button>
          </div>
        </div>
      </nav>

      <!-- Table -->
      <div class="max-w-[95%] mx-auto px-4 py-6 overflow-x-auto">
        <table class="min-w-full bg-gray-750 rounded-lg overflow-hidden whitespace-nowrap">
          <thead class="bg-gray-700">
            <tr>
              <th class="px-4 py-3 text-left">No Transaksi</th>
              <th class="px-4 py-3 text-left">Tanggal</th>
              <th class="px-4 py-3 text-left">Nama Item</th>
              <th class="px-4 py-3 text-right">Jumlah</th>
              <th class="px-4 py-3 text-right">Harga</th>
              <th class="px-4 py-3 text-right">Total</th>
              <th class="px-4 py-3 text-right">Disc(%)</th>
              <th class="px-4 py-3 text-right">Disc(Rp)</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-600">
            {% set ns = namespace(previous_notransaksi=None, row_color=True) %}
            {% for item in items %} {% if item.notransaksi !=
            ns.previous_notransaksi %} {% set ns.row_color = not ns.row_color %}
            {% set ns.previous_notransaksi = item.notransaksi %} {% endif %}
            <tr
              class="hover:bg-gray-700 transition duration-150 {{ 'bg-black' if ns.row_color else 'bg-gray-900' }}"
            >
              <td class="px-4 py-3">
                <span
                  class="cursor-pointer hover:text-blue-400"
                  onclick="showTransactionDetails('{{ item.notransaksi }}')"
                >
                  {{ item.notransaksi }}
                </span>
              </td>
              <td class="px-4 py-3">
                {{ item.tanggal.strftime('%Y-%m-%d %H:%M') }}
              </td>
              <td class="px-4 py-3">
                <span class="text-yellow-400"
                  >{{ "%.0f"|format(item.jumlah) }}</span
                >
                - {{ item.namaitem }} -
                <span class="text-red-400"
                  >{{ "%.1f"|format(item.disc_percent) }}%</span
                >
              </td>
              <td class="px-4 py-3 text-right">
                {{ "%.2f"|format(item.jumlah) }}
              </td>
              <td class="px-4 py-3 text-right">
                {{ "{:,.0f}".format(item.harga) }}
              </td>
              <td class="px-4 py-3 text-right">
                {{ "{:,.0f}".format(item.total) }}
              </td>
              <td class="px-4 py-3 text-right">
                {{ "%.1f"|format(item.disc_percent) }}%
              </td>
              <td class="px-4 py-3 text-right">
                {{ "{:,.0f}".format(item.disc_amount) }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Transaction Modal -->
    <div
      id="transactionModal"
      class="fixed inset-0 bg-gray-900 bg-opacity-70 hidden flex items-center justify-center"
    >
      <div
        class="bg-gray-750 p-6 rounded-lg shadow-xl border-2 border-blue-500 w-[95%] max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <div class="flex justify-between items-center mb-4">
          <div>
            <div class="space-y-2">
              <h3 class="text-xl font-bold">Transaction Details</h3>
              <div class="text-gray-400" id="transactionInfo"></div>
            </div>
          </div>
          <button
            onclick="closeTransactionModal()"
            class="text-gray-400 hover:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-700">
            <thead class="bg-gray-700">
              <tr>
                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Kode Item</th>
                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Nama Item</th>
                <th class="px-4 py-3 text-right text-sm font-medium text-gray-300">Jumlah</th>
                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Satuan</th>
                <th class="px-4 py-3 text-right text-sm font-medium text-gray-300">Harga</th>
                <th class="px-4 py-3 text-right text-sm font-medium text-gray-300">Pot (%)</th>
                <th class="px-4 py-3 text-right text-sm font-medium text-gray-300">Total</th>
              </tr>
            </thead>
            <tbody id="transactionDetails" class="bg-gray-800 divide-y divide-gray-700"></tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Remove the external script reference -->
    <!-- <script src="{{ url_for('static', path='js/transaction-modal.js') }}"></script> -->
    <script>
      // Transaction Modal Functions
      function getTransactionType(type) {
        const types = {
          KSR: "Sales",
          BL: "Purchase",
          RJ: "Return",
          IM: "Stock In",
          IK: "Stock Out",
          OP: "Stock Opname",
        };
        return types[type] || type;
      }

      function closeTransactionModal() {
        const modal = document.getElementById("transactionModal");
        modal.classList.add("hidden");
        modal.style.display = "none";
      }

      async function showTransactionDetails(notransaksi) {
        try {
          console.log("Showing transaction details for:", notransaksi);
          const encodedNotransaksi = encodeURIComponent(notransaksi);
          const response = await fetch(`/api/transaction-details/${encodedNotransaksi}`);
          
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || "Failed to fetch transaction details");
          }

          const data = await response.json();

          // Update transaction info
          const date = new Date(data.header.tanggal).toLocaleDateString("en-US", {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric",
          });
          
          document.getElementById("transactionInfo").textContent = 
            `${notransaksi} - ${date} - ${getTransactionType(data.header.tipe)}`;

          // Calculate total first
          let total = data.details.reduce((sum, detail) => sum + detail.total, 0);

          // Clear existing content
          const tbody = document.getElementById("transactionDetails");
          tbody.innerHTML = "";

          // Remove existing summary section if it exists
          const existingSummary = document.getElementById("summarySection");
          if (existingSummary) {
            existingSummary.remove();
          }

          // Add details rows
          data.details.forEach((detail) => {
            const row = document.createElement("tr");
            row.innerHTML = `
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${detail.kodeitem}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${detail.namaitem}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${detail.jumlah}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">${detail.satuan}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${detail.harga.toLocaleString()}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm ${detail.potongan > 0 ? 'text-red-400' : 'text-gray-300'} text-right">${
                detail.potongan > 0 ? detail.potongan.toFixed(1) : "-"
              }</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300 text-right">${detail.total.toLocaleString()}</td>
            `;
            tbody.appendChild(row);
          });

          // Add summary section
          const summarySection = document.createElement("div");
          summarySection.id = "summarySection";
          summarySection.className = "flex justify-end mt-6 mb-4";

          const discountAmount = data.header.potnomfaktur || 0;
          const discountDisplay = discountAmount > 0 ? `-${discountAmount.toLocaleString()}` : "-";

          const summaryTable = document.createElement("table");
          summaryTable.className = "text-right";
          summaryTable.innerHTML = `
            <tr>
              <td class="pr-4 text-gray-400">Subtotal:</td>
              <td class="text-gray-300">${total.toLocaleString()}</td>
            </tr>
            <tr>
              <td class="pr-4 text-gray-400">Discount:</td>
              <td class="${discountAmount > 0 ? 'text-red-400' : 'text-gray-300'}">${discountDisplay}</td>
            </tr>
            <tr class="font-bold">
              <td class="pr-4 text-gray-400">Total:</td>
              <td class="text-gray-300">${(total - discountAmount).toLocaleString()}</td>
            </tr>
          `;

          summarySection.appendChild(summaryTable);
          tbody.parentNode.insertAdjacentElement("afterend", summarySection);

          // Show modal
          const modal = document.getElementById("transactionModal");
          modal.classList.remove("hidden");
          modal.style.display = "flex";
        } catch (error) {
          console.error("Error fetching transaction details:", error);
          alert(error.message || "Failed to load transaction details");
        }
      }

      // Initialize modal click-outside behavior and close button
      document.addEventListener("DOMContentLoaded", function () {
        // Add click event listeners to transaction IDs
        document.querySelectorAll('[onclick*="showTransactionDetails"]').forEach(element => {
          const match = element.getAttribute('onclick').match(/'([^']+)'/);
          if (match && match[1]) {
            const notransaksi = match[1];
            element.removeAttribute('onclick');
            element.addEventListener('click', function() {
              showTransactionDetails(notransaksi);
            });
          }
        });
        
        // Add click event listener to close modal when clicking outside
        const modal = document.getElementById("transactionModal");
        if (modal) {
          modal.addEventListener("click", function(e) {
            if (e.target === this) {
              closeTransactionModal();
            }
          });
        }
      });
    </script>
  </body>
</html>







