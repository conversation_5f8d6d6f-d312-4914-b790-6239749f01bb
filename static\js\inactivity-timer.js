// Inactivity timeout in milliseconds (disabled)
const INACTIVITY_TIMEOUT = 0; // Set to 0 to disable completely
let inactivityTimer;
let isTimerActive = false;

// Function to reset the timer
function resetInactivityTimer() {
  // Only proceed if timer is active and timeout is not zero
  if (!isTimerActive || INACTIVITY_TIMEOUT === 0) return;

  // Clear the existing timer
  clearTimeout(inactivityTimer);

  // Set a new timer
  inactivityTimer = setTimeout(() => {
    // Redirect to logout when timer expires
    window.location.href = "/logout";
  }, INACTIVITY_TIMEOUT);
}

// Function to disable the inactivity timer
function disableInactivityTimer() {
  isTimerActive = false;
  clearTimeout(inactivityTimer);
}

// Function to enable the inactivity timer
function enableInactivityTimer() {
  isTimerActive = true;
  resetInactivityTimer();
}

// Initialize the timer when the page loads
document.addEventListener("DOMContentLoaded", function () {
  // Don't call resetInactivityTimer() or enableInactivityTimer()
  
  // Keep event listeners for future use if needed
  const events = ["mousedown", "mousemove", "keypress", "scroll", "touchstart"];
  events.forEach((event) => {
    document.addEventListener(event, resetInactivityTimer, true);
  });
});



