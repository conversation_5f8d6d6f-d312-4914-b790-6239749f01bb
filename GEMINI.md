# Gemini Code Assistant Context

## Project Overview

This project is a FastAPI web application that serves as a Point of Sale (POS) system. It appears to be designed for a retail store, with features for managing inventory, sales, purchases, and customer data. The application uses a PostgreSQL database and Jinja2 for templating.

### Key Technologies

*   **Backend:** Python, FastAPI
*   **Database:** PostgreSQL, SQLAlchemy
*   **Frontend:** HTML, Jinja2, TailwindCSS
*   **Authentication:** JWT (based on python-jose)
*   **Deployment:** Uvicorn

## Building and Running

To run this project locally, you need to have Python and PostgreSQL installed.

1.  **Install Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

2.  **Configure Database:**
    *   The database connection is configured in `database/database_connection.py`.
    *   Update the `SQLALCHEMY_DATABASE_URL` to point to your PostgreSQL instance.

3.  **Run the Application:**
    ```bash
    python main.py
    ```
    The application will be available at `http://localhost:7000`.

## Development Conventions

*   **Modular Structure:** The application is organized into modules for different functionalities (e.g., `routers`, `database`).
*   **Routing:** Each major feature has its own router in the `routers` directory.
*   **Database Models:** All database tables are defined as SQLAlchemy models in `database/database_model.py`.
*   **Authentication:** A middleware in `middleware.py` handles authentication for protected routes.
*   **Templating:** HTML templates are located in the `templates` directory and rendered using Jinja2.
*   **Static Files:** Static assets like CSS, JavaScript, and images are served from the `static` directory.
