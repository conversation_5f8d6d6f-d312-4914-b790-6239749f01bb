<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Item - {{ item.namaitem }}</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    backgroundColor: {
                        'gray-750': '#2d3748',
                    },
                },
            },
        }
    </script>
</head>
<body class="bg-black text-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <nav class="bg-gray-800 shadow-lg">
            <div class="max-w-[95%] mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-3xl font-bold"><span class="text-white">Edit Item - </span><span class="text-green-500">{{ item.namaitem }}</span></h1>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-[95%] mx-auto px-4 py-6">
            <div class="bg-gray-800 rounded-lg p-6 shadow-lg">
                <form id="editItemForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-4">
                            <div>
                                <label for="kodeItem" class="block text-gray-400 mb-1">Kode Item:</label>
                                <input type="text" id="kodeItem" name="kodeItem" value="{{ item.kodeitem }}" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-500 text-white cursor-not-allowed opacity-75"
                                    readonly>
                            </div>
                            
                            <div>
                                <label for="barcode" class="block text-gray-400 mb-1">Barcode:</label>
                                <input type="text" id="barcode" name="barcode" value="{{ unit.kodebarcode if unit else '' }}" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-500 text-white cursor-not-allowed opacity-75"
                                    readonly>
                            </div>
                            
                            <div>
                                <label for="namaItem" class="block text-gray-400 mb-1">Nama Item:</label>
                                <input type="text" id="namaItem" name="namaItem" value="{{ item.namaitem }}" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                            </div>
                            
                            <div>
                                <label for="stok" class="block text-gray-400 mb-1">Stok:</label>
                                <input type="text" id="stok" name="stok"
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-500 text-white cursor-not-allowed opacity-75"
                                    readonly>
                            </div>
                            
                            <div>
                                <label for="merek" class="block text-gray-400 mb-1">Merek:</label>
                                <select id="merek" name="merek" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                                    <option value=""></option>
                                    {% for brand in brands %}
                                    <option value="{{ brand }}" {% if brand == item.merek %}selected{% endif %}>{{ brand or '' }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div>
                                <label for="jenis" class="block text-gray-400 mb-1">Jenis:</label>
                                <select id="jenis" name="jenis" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                                    {% for type in types %}
                                    <option value="{{ type }}" {% if type == item.jenis %}selected{% endif %}>{{ type }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <!-- Right Column -->
                        <div class="space-y-4">
                            <div>
                                <!-- First row: Harga Pokok and Profit Margin -->
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label for="hargaPokok" class="block text-gray-400 mb-1">Harga Pokok:</label>
                                        <input type="text" id="hargaPokok" name="hargaPokok" value="{{ '{:,.0f}'.format(unit.hargapokok|float) if unit and unit.hargapokok else 0 }}" 
                                            class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                                    </div>
                                    
                                    <div>
                                        <label for="profitMargin" class="block text-gray-400 mb-1">Profit Margin:</label>
                                        <div class="relative">
                                            <input type="number" id="profitMargin" name="profitMargin" step="0.1" min="0"
                                                class="w-full px-4 py-2 pr-8 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                                            <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">%</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Second row: Harga Jual -->
                                <div>
                                    <label for="hargaJual" class="block text-gray-400 mb-1">Harga Jual:</label>
                                    <input type="text" id="hargaJual" name="hargaJual" 
                                        value="{{ '{:,.0f}'.format(current_selling_price|float) if current_selling_price else 0 }}" 
                                        class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                                </div>
                            </div>
                            
                            <div>
                                <label for="minimumStock" class="block text-gray-400 mb-1">Minimum Stock:</label>
                                <input type="number" id="minimumStock" name="minimumStock" 
                                    value="{{ ('%.2f'|format(item.stokmin|float)).rstrip('0').rstrip('.') if item.stokmin else 0 }}" 
                                    step="0.01"
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                            </div>

                            <div class="flex items-center space-x-2">
                                <div class="flex-1">
                                    <label for="satuan" class="block text-gray-400 mb-1">Satuan:</label>
                                    <select id="satuan" name="satuan" 
                                        onchange="updateStockDisplay(this.value)"
                                        class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                                        {% for unit in units_with_stock %}
                                        <option value="{{ unit.satuan }}" 
                                                data-stock="{{ '%.2f'|format(unit.converted_stock) }}"
                                                data-conversion="{{ unit.jumlahkonv }}"
                                                data-type="{{ unit.tipe }}"
                                                data-hargapokok="{{ unit.hargapokok|default(0) }}"
                                                data-hargajual="{{ unit.hargajual|default(0) }}">
                                            {{ unit.satuan }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label for="rak" class="block text-gray-400 mb-1">Rak:</label>
                                <input type="text" id="rak" name="rak" value="{{ item.rak }}" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                            </div>

                            <div>
                                <label for="keterangan" class="block text-gray-400 mb-1">Keterangan:</label>
                                <textarea id="keterangan" name="keterangan" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150"
                                    rows="3">{{ item.keterangan }}</textarea>
                            </div>

                            <div>
                                <label for="supplier" class="block text-gray-400 mb-1">Supplier:</label>
                                <select id="supplier" name="supplier" 
                                    class="w-full px-4 py-2 rounded-lg border border-gray-600 bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150">
                                    <option value=""></option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier }}" {% if supplier == item.supplier1 %}selected{% endif %}>{{ supplier }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="mt-6 flex justify-start space-x-4">
                        <button type="button" id="updateItemBtn" 
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150">
                            Update Item
                        </button>
                        <button type="button" id="cancelBtn" onclick="window.close()"
                            class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition duration-150">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Add copy on select functionality
        document.addEventListener('mouseup', function() {
            const selectedText = window.getSelection().toString().trim();
            if (selectedText) {
                navigator.clipboard.writeText(selectedText).catch(err => {
                    console.error('Failed to copy text: ', err);
                });
            }
        });
        
        // Add this near the beginning of the script section
        document.addEventListener('keydown', function(e) {
            // Check if Enter key is pressed and not inside a textarea
            if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault(); // Prevent default form submission
                document.getElementById('updateItemBtn').click();
            }
        });

        // For now, just prevent form submission
        document.getElementById('editItemForm').addEventListener('submit', function(e) {
            e.preventDefault();
        });
        
        document.getElementById('updateItemBtn').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('editItemForm').dispatchEvent(new Event('submit'));
        });

        function parseLocalNumber(value) {
            // First, handle the case where we have both . and ,
            if (typeof value === 'string' && value.includes(',')) {
                // Replace all dots (thousand separators) with nothing
                // and comma (decimal separator) with dot
                return parseFloat(value.replace(/\./g, '').replace(',', '.'));
            }
            // If no comma, just remove all dots and parse
            return parseFloat(value.replace(/\./g, ''));
        }

        function formatNumber(input) {
            // Remove all non-digit characters except comma
            let value = input.value.replace(/[^\d,]/g, '');
            // Handle decimal numbers (replace comma with dot temporarily)
            let numValue = parseLocalNumber(value);
            // Format with dots as thousand separators and comma as decimal
            let formattedValue = numValue.toLocaleString('id-ID', {
                maximumFractionDigits: 3,
                useGrouping: true
            });
            // Update input value
            input.value = formattedValue;
        }

        function calculateProfitMargin(hargaPokok, hargaJual) {
            const hp = parseLocalNumber(hargaPokok);
            const hj = parseLocalNumber(hargaJual);
            if (hp === 0) return 0;
            return ((hj - hp) / hp * 100).toFixed(1);
        }

        function calculateHargaJual(hargaPokok, profitMargin) {
            const hp = parseLocalNumber(hargaPokok);
            const margin = parseFloat(profitMargin) || 0;
            return Math.round(hp * (1 + margin / 100));
        }

        // Add event listeners to format numbers on input
        document.getElementById('hargaPokok').addEventListener('input', function() {
            formatNumber(this);
            const profitMargin = document.getElementById('profitMargin').value;
            const hargaJual = document.getElementById('hargaJual');
            hargaJual.value = calculateHargaJual(this.value, profitMargin).toLocaleString('id-ID');
        });

        document.getElementById('hargaJual').addEventListener('input', function() {
            formatNumber(this);
            const hargaPokok = document.getElementById('hargaPokok').value;
            const profitMargin = document.getElementById('profitMargin');
            profitMargin.value = calculateProfitMargin(hargaPokok, this.value);
        });

        document.getElementById('profitMargin').addEventListener('input', function() {
            const hargaPokok = document.getElementById('hargaPokok').value;
            const hargaJual = document.getElementById('hargaJual');
            hargaJual.value = calculateHargaJual(hargaPokok, this.value).toLocaleString('id-ID');
        });

        // Format numbers on page load
        window.addEventListener('load', function() {
            formatNumber(document.getElementById('hargaPokok'));
            formatNumber(document.getElementById('hargaJual'));
            const hargaPokok = document.getElementById('hargaPokok').value;
            const hargaJual = document.getElementById('hargaJual').value;
            document.getElementById('profitMargin').value = calculateProfitMargin(hargaPokok, hargaJual);
        });

        // Before form submission, convert formatted numbers back to plain numbers
        document.getElementById('editItemForm').addEventListener('submit', function(e) {
            let hargaPokok = document.getElementById('hargaPokok');
            let hargaJual = document.getElementById('hargaJual');
            
            hargaPokok.value = hargaPokok.value.replace(/,/g, '');
            hargaJual.value = hargaJual.value.replace(/,/g, '');
        });

        async function updateStockDisplay(satuan) {
            try {
                // Create URL with query parameters
                const url = new URL('/api/convert-stock', window.location.origin);
                url.searchParams.append('kodeitem', currentItemCode);
                url.searchParams.append('satuan', satuan);
                
                const response = await fetch(url.toString());
                if (!response.ok) {
                    throw new Error('Failed to fetch converted values');
                }
                
                const data = await response.json();
                
                // Update stock display
                document.getElementById('stok').value = `${data.converted_stock.toLocaleString('id-ID')} ${satuan}`;
                
                // Update prices
                document.getElementById('hargaPokok').value = data.harga_pokok.toLocaleString('id-ID');
                document.getElementById('hargaJual').value = data.harga_jual.toLocaleString('id-ID');
                
                // Update profit margin
                const hargaPokok = document.getElementById('hargaPokok').value;
                const hargaJual = document.getElementById('hargaJual').value;
                document.getElementById('profitMargin').value = calculateProfitMargin(hargaPokok, hargaJual);
                
            } catch (error) {
                console.error('Error updating display:', error);
                // Optionally show error to user
                alert('Failed to update unit conversion. Please try again.');
            }
        }

        let currentItemCode = '{{ item.kodeitem }}'; // Get the item code from template context

        // Initialize display on page load
        window.addEventListener('load', function() {
            const satuanSelect = document.getElementById('satuan');
            if (satuanSelect.value) {
                updateStockDisplay(satuanSelect.value);
            }
        });

        document.getElementById('editItemForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                // Get form values and properly handle number formatting
                const formData = {
                    namaitem: document.getElementById('namaItem').value,
                    merek: document.getElementById('merek').value,
                    jenis: document.getElementById('jenis').value,
                    rak: document.getElementById('rak').value,
                    stokmin: parseFloat(document.getElementById('minimumStock').value) || 0,
                    satuan: document.getElementById('satuan').value,
                    harga_pokok: parseFloat(document.getElementById('hargaPokok').value.replace(/[,\.]/g, '')) || 0,
                    harga_jual: parseFloat(document.getElementById('hargaJual').value.replace(/[,\.]/g, '')) || 0,
                    keterangan: document.getElementById('keterangan').value,
                    supplier1: document.getElementById('supplier').value
                };

                const response = await fetch(`/api/update-item/${currentItemCode}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok) {
                    window.close(); // Just close the window on success, no alert
                } else {
                    // Show the specific error message from the server
                    let errorMessage = 'Failed to update item';
                    if (result.detail) {
                        if (typeof result.detail === 'object') {
                            errorMessage = JSON.stringify(result.detail, null, 2);
                        } else {
                            errorMessage = result.detail;
                        }
                    }
                    alert(`Error: ${errorMessage}`);
                }
            } catch (error) {
                alert(`Error: ${error.message || 'An unexpected error occurred'}`);
            }
        });
    </script>
</body>
</html>
