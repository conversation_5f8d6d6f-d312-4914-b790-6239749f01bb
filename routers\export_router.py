from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import func
from database.database_connection import get_db
from database.database_model import (
    ItemSalesHeader,
    ItemSalesDetail,
    Item,
    ItemStock,
    ItemUnitConversion,
    ItemSellingPrice
)
from datetime import datetime, timedelta
import pandas as pd
import pytz
import os
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
import subprocess
from shared_state import search_history  # Import from shared state

router = APIRouter()

@router.get("/export-excel/{search_key}")
async def export_to_excel(search_key: str, db: Session = Depends(get_db)):
    if search_key not in search_history:
        return JSONResponse(
            content={"error": "Search results not found"},
            status_code=404
        )

    df, search_term = search_history[search_key]
    
    # Get barcode information for each item
    barcode_data = {}
    for kodeitem in df['kodeitem'].unique():
        # Query all barcodes for this item
        barcodes = db.query(ItemUnitConversion.kodeitem, 
                           ItemUnitConversion.satuan, 
                           ItemUnitConversion.kodebarcode)\
            .filter(ItemUnitConversion.kodeitem == kodeitem)\
            .filter(ItemUnitConversion.kodebarcode != None)\
            .filter(ItemUnitConversion.kodebarcode != '')\
            .all()
        
        # Format as satuan:barcode pairs
        if barcodes:
            barcode_list = [f"{b.satuan}:{b.kodebarcode}" for b in barcodes if b.kodebarcode]
            barcode_data[kodeitem] = ", ".join(barcode_list)
        else:
            barcode_data[kodeitem] = ""
    
    # Add barcode column to DataFrame
    df['barcode'] = df['kodeitem'].map(barcode_data)
    
    # Get current date for calculations
    current_date = datetime.now()
    one_year_ago = current_date - timedelta(days=365)
    
    # Calculate monthly sales for each item
    monthly_sales = {}
    for kodeitem in df['kodeitem'].unique():
        monthly_data = []
        has_sales = False
        
        # Get current month
        current_month = current_date.month
        
        for i in range(3):
            # Calculate month and year for last year's corresponding months
            month = (current_month + i) % 12 or 12  # Convert 0 to 12
            year = current_date.year - 1
            
            # Create start and end dates for the month
            start_date = datetime(year, month, 1)
            if month == 12:
                end_date = datetime(year + 1, 1, 1)
            else:
                end_date = datetime(year, month + 1, 1)
            
            month_sales = db.query(func.sum(ItemSalesDetail.jumlah))\
                .join(
                    ItemSalesHeader,
                    (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
                    (ItemSalesHeader.tipe == 'KSR')
                ).filter(
                    ItemSalesDetail.kodeitem == kodeitem,
                    ItemSalesHeader.tanggal >= start_date,
                    ItemSalesHeader.tanggal < end_date
                ).scalar() or 0
            
            if month_sales > 0:
                has_sales = True
            monthly_data.append(str(int(month_sales)))
        
        if has_sales:
            monthly_sales[kodeitem] = '-'.join(monthly_data)  # Will show Mar-Apr-May
        else:
            monthly_sales[kodeitem] = ''
    
    # Add the new columns to DataFrame
    df['last_year_3months_sales'] = df['kodeitem'].map(monthly_sales)
    
    df['need_stock'] = df.apply(
        lambda row: '' if row['hargapokok'] <= 10 else 
                   '*' if row['stokmin'] > 0 and row['stok'] <= row['stokmin'] else 
                   '', 
        axis=1
    )
    
    # Calculate stock needed for 3 months
    yearly_sales = db.query(
        ItemSalesDetail.kodeitem,
        func.sum(ItemSalesDetail.jumlah).label('total_sales')
    ).join(
        ItemSalesHeader,
        (ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi) &
        (ItemSalesHeader.tipe == 'KSR')
    ).filter(
        ItemSalesHeader.tanggal >= one_year_ago,
        ItemSalesDetail.kodeitem.in_(df['kodeitem'].tolist())
    ).group_by(
        ItemSalesDetail.kodeitem
    ).all()
    
    sales_dict = {item.kodeitem: item.total_sales or 0 for item in yearly_sales}
    
    def calculate_stock_needed(row):
        yearly_sale = sales_dict.get(row['kodeitem'], 0)
        three_month_need = (yearly_sale * 3) / 12
        current_stock = row['stok']
        needed = three_month_need - current_stock
        return needed if needed > 0 else None
    
    df['stok_needed_3month'] = df.apply(calculate_stock_needed, axis=1)
    
    # Reorder columns
    cols = list(df.columns)
    stok_idx = cols.index('stok')
    for col in ['stok_needed_3month', 'last_year_3months_sales', 'need_stock', 'barcode']:
        if col in cols:  # Check if column exists before removing
            cols.remove(col)
    cols.insert(stok_idx + 1, 'stok_needed_3month')
    cols.insert(stok_idx + 2, 'last_year_3months_sales')
    cols.insert(stok_idx + 3, 'need_stock')
    cols.insert(stok_idx + 4, 'barcode')  # Add barcode column after need_stock
    df = df[cols]
    
    # Format filename and path
    timestamp = datetime.now(pytz.timezone('Asia/Jakarta'))
    formatted_date = timestamp.strftime('%d.%m.%Y')
    formatted_time = timestamp.strftime('%H.%M.%S')
    sanitized_search = ''.join(c if c.isalnum() else '-' for c in search_term)
    filename = f"{sanitized_search}-{formatted_date}-{formatted_time}.xlsx"
    filepath = os.path.join(r"E:\Download", filename)
    
    os.makedirs(r"E:\Download", exist_ok=True)
    
    # Format numeric columns
    df['hargapokok'] = df['hargapokok'].apply(lambda x: f'{float(x):,.0f}' if pd.notnull(x) else '0')
    df['hargajual'] = df['hargajual'].apply(lambda x: f'{float(x):,.0f}' if pd.notnull(x) else '0')
    df['stokmin'] = df['stokmin'].apply(lambda x: f'{float(x):,.0f}' if pd.notnull(x) else '0')
    
    # Export and format Excel
    df.to_excel(filepath, index=False, engine='openpyxl')
    
    wb = load_workbook(filepath)
    ws = wb.active
    
    # Apply formatting
    header_row = 1
    stok_needed_col = None
    hargapokok_col = None
    kodeitem_col = None
    
    for col in range(1, ws.max_column + 1):
        header_value = ws.cell(row=header_row, column=col).value
        if header_value == 'stok_needed_3month':
            stok_needed_col = col
        elif header_value == 'hargapokok':
            hargapokok_col = col
        elif header_value == 'kodeitem':
            kodeitem_col = col

    if stok_needed_col and hargapokok_col:
        grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
        red_fill = PatternFill(start_color='FF0000', end_color='FF0000', fill_type='solid')
        
        for row in range(2, ws.max_row + 1):
            hargapokok_cell = ws.cell(row=row, column=hargapokok_col)
            stok_needed_cell = ws.cell(row=row, column=stok_needed_col)
            
            hargapokok_value = float(hargapokok_cell.value.replace(',', '') if hargapokok_cell.value else 0)
            
            if hargapokok_value <= 10:
                stok_needed_cell.fill = red_fill
            elif stok_needed_cell.value and float(stok_needed_cell.value) > 0:
                stok_needed_cell.fill = grey_fill

            # Add hyperlink to kodeitem
            if kodeitem_col:
                kodeitem_cell = ws.cell(row=row, column=kodeitem_col)
                kodeitem = kodeitem_cell.value
                if kodeitem:
                    kodeitem_cell.hyperlink = f"http://localhost:7000/item-history/{kodeitem}"
                    kodeitem_cell.style = "Hyperlink"
    
    wb.save(filepath)
    
    try:
        subprocess.Popen(['start', 'excel', filepath], shell=True)
    except Exception as e:
        print(f"Failed to open Excel file: {e}")
    
    return JSONResponse(
        content={
            "message": "File saved and opened successfully",
            "path": filepath
        }
    )
