// Landing page specific functions

function searchNegative() {
  // Add a hidden input to the form
  const form = document.querySelector("form");
  const input = document.createElement("input");
  input.type = "hidden";
  input.name = "negative";
  input.value = "true";
  form.appendChild(input);

  // Submit the form
  form.submit();
}

async function exportToExcel(searchKey) {
  if (!searchKey) {
    alert("Please perform a search first");
    return;
  }
  try {
    const response = await fetch(`/export-excel/${searchKey}`);
    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.error || "Failed to export to Excel");
    }
  } catch (error) {
    console.error("Failed to export to Excel:", error);
    alert(error.message || "Failed to export to Excel");
  }
}

function toggleDropdown() {
  const dropdown = document.getElementById("dropdownMenu");
  dropdown.classList.toggle("hidden");

  // Close dropdown when clicking outside
  document.addEventListener("click", function (event) {
    const isClickInside = event.target.closest(".relative");
    if (!isClickInside) {
      dropdown.classList.add("hidden");
    }
  });
}

function formatCurrency(amount) {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

function updateTodaySales() {
  fetch("/today-sales")
    .then((response) => response.json())
    .then((data) => {
      document.getElementById("todaySales").textContent = formatCurrency(
        data.total_sales
      );
    })
    .catch((error) => console.error("Error:", error));
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function() {
  // Update sales every 30 seconds
  updateTodaySales();
  setInterval(updateTodaySales, 30000);
});
