<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Profit Report</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    backgroundColor: {
                        "gray-750": "#2d3748",
                        "gray-850": "#1a1a1a",
                        "gray-880": "#242424",
                        "gray-900": "#1e293b",
                    },
                },
            },
        };
    </script>
</head>
<body class="bg-black text-gray-100">
    <div class="min-h-screen">
        <!-- Navbar -->
        <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
            <div class="max-w-[95%] mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center space-x-4">
                        <h1 class="text-3xl font-bold text-white">Daily Profit Report</h1>
                        <!-- Month Selector -->
                        <div class="flex items-center space-x-2 bg-gray-700 rounded-lg px-4 py-2">
                            <a href="/daily-profit?month={{ prev_month }}&year={{ prev_year }}"
                               class="text-gray-300 hover:text-white transition-colors duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            <span class="text-xl">{{ month_name }} {{ current_year }}</span>
                            <a href="/daily-profit?month={{ next_month }}&year={{ next_year }}"
                               class="text-gray-300 hover:text-white transition-colors duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <button
                            onclick="window.close()"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-[95%] mx-auto px-4 py-6">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-left">Date</th>
                                <th class="px-4 py-3 text-right">Total Sales</th>
                                <th class="px-4 py-3 text-right">Total Cost</th>
                                <th class="px-4 py-3 text-right">Profit</th>
                                <th class="px-4 py-3 text-right">Margin</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {% for day in daily_data %}
                            <tr class="hover:bg-gray-700">
                                <td class="px-4 py-3">{{ day.date }}</td>
                                <td class="px-4 py-3 text-right text-gray-300">{{ day.total_sales | currency }}</td>
                                <td class="px-4 py-3 text-right text-gray-300">{{ day.total_cost | currency }}</td>
                                <td class="px-4 py-3 text-right {% if day.profit > 0 %}text-green-400{% elif day.profit < 0 %}text-red-400{% else %}text-gray-300{% endif %}">
                                    {{ day.profit | currency }}
                                </td>
                                <td class="px-4 py-3 text-right {% if day.margin > 0 %}text-green-400{% elif day.margin < 0 %}text-red-400{% else %}text-gray-300{% endif %}">
                                    {{ "%.1f"|format(day.margin) }}%
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="bg-gray-700 font-bold">
                            <tr>
                                <td class="px-4 py-3">TOTAL</td>
                                <td class="px-4 py-3 text-right text-gray-100">
                                    {{ (daily_data | sum(attribute='total_sales')) | currency }}
                                </td>
                                <td class="px-4 py-3 text-right text-gray-100">
                                    {{ (daily_data | sum(attribute='total_cost')) | currency }}
                                </td>
                                <td class="px-4 py-3 text-right {% if (daily_data | sum(attribute='profit')) > 0 %}text-green-400{% else %}text-red-400{% endif %}">
                                    {{ (daily_data | sum(attribute='profit')) | currency }}
                                </td>
                                <td class="px-4 py-3 text-right {% if (daily_data | sum(attribute='margin')) > 0 %}text-green-400{% else %}text-red-400{% endif %}">
                                    {{ "%.1f"|format(daily_data | sum(attribute='margin') / daily_data|length) }}%
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
