from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, desc, case, literal, text, extract
from datetime import datetime, timedelta
import pytz
from database.database_connection import get_db
from database.database_model import (
    Item,
    ItemSalesHeader,
    ItemSalesDetail,
    ItemPurchaseHeader,
    ItemPurchaseDetail,
    ItemStock,
    ItemUnitConversion,
    ItemStockOpname,
)
from collections import defaultdict
import calendar
from typing import Optional, Any
from pydantic import BaseModel
from decimal import Decimal

router = APIRouter(
    prefix="",
    tags=["item_history"]
)

templates = Jinja2Templates(directory="templates")

class StockUpdate(BaseModel):
    kodeitem: str
    new_stock: float
    satuan: str

@router.post("/api/update-stock")
async def update_stock(
    stock_update: StockUpdate,
    db: Session = Depends(get_db)
):
    # Get current stock
    current_stock = db.query(ItemStock.stok)\
        .filter(
            ItemStock.kodeitem == stock_update.kodeitem,
            ItemStock.kantor == 'UTM'
        ).scalar() or 0

    # Get item's base price
    base_price = db.query(ItemUnitConversion.hargapokok)\
        .filter(
            ItemUnitConversion.kodeitem == stock_update.kodeitem,
            ItemUnitConversion.satuan == stock_update.satuan
        ).scalar() or 0

    # Convert values to Decimal for calculation
    base_price = Decimal(str(base_price))
    stock_difference = Decimal(str(stock_update.new_stock)) - Decimal(str(current_stock))
    
    # Get current date
    now = datetime.now()
    # Use correct month number in period ID
    period_id = now.strftime(f"OPNA{now.month}E-%Y%m%d")
    detail_id = f"{now.strftime('%Y%m%d')}-{stock_update.kodeitem}-UTM"

    # Create stock opname record
    new_opname = ItemStockOpname(
        iddetail=detail_id,
        periode=period_id,
        tanggal=now,
        kodeitem=stock_update.kodeitem,
        kodekantor='UTM',
        satuan=stock_update.satuan,
        jmlsebelum=float(current_stock),  # Convert to float for database
        jmlfisik=float(stock_update.new_stock),  # Convert to float for database
        jmlselisih=float(stock_difference),  # Convert to float for database
        kodeacc='5-2200',
        user1='J',
        user2=None,
        dateupd=now,
        harga=base_price,
        total=base_price * stock_difference,
        compname='RYZEN5600G'
    )

    try:
        # Update stock
        db.query(ItemStock).filter(
            ItemStock.kodeitem == stock_update.kodeitem,
            ItemStock.kantor == 'UTM'
        ).update({
            'stok': float(stock_update.new_stock)  # Convert to float for database
        })

        # Add opname record
        db.add(new_opname)
        db.commit()
        return {"success": True}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/item-history/{kodeitem}", response_class=HTMLResponse)
async def get_item_history(
    request: Request,
    kodeitem: str,
    db: Session = Depends(get_db)
):
    # Add current date in Jakarta timezone
    jakarta_tz = pytz.timezone('Asia/Jakarta')
    now = datetime.now(jakarta_tz)
    
    # Convert kodeitem to lowercase for comparison
    kodeitem = kodeitem.lower()
    
    # Get item details using case-insensitive comparison
    item = db.query(Item).filter(func.lower(Item.kodeitem) == kodeitem).first()
    
    current_stock = db.query(ItemStock.stok)\
        .filter(func.lower(ItemStock.kodeitem) == kodeitem)\
        .scalar() or 0

    # Get sales and purchase history combined
    sales_history = db.query(
        ItemSalesHeader.tanggal.label('tanggal'),
        ItemSalesHeader.notransaksi.label('notransaksi'),
        ItemSalesHeader.tipe.label('tipe'),
        ItemSalesDetail.jumlah.label('jumlah'),
        ItemSalesDetail.satuan.label('satuan'),
        ItemSalesDetail.harga.label('harga'),
        ItemSalesDetail.total.label('total'),
        ItemSalesHeader.kodesupel.label('kodesupel'),
        ItemSalesHeader.keterangan.label('keterangan')
    ).join(
        ItemSalesDetail,
        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
    ).filter(
        func.lower(ItemSalesDetail.kodeitem) == kodeitem,
        ItemSalesHeader.tipe.in_(['KSR', 'RJ', 'IK', 'JL'])
    )

    purchase_history = db.query(
        ItemPurchaseHeader.tanggal.label('tanggal'),
        ItemPurchaseHeader.notransaksi.label('notransaksi'),
        ItemPurchaseHeader.tipe.label('tipe'),
        ItemPurchaseDetail.jumlah.label('jumlah'),
        ItemPurchaseDetail.satuan.label('satuan'),
        ItemPurchaseDetail.harga.label('harga'),
        ItemPurchaseDetail.total.label('total'),
        ItemPurchaseHeader.kodesupel.label('kodesupel'),
        ItemPurchaseHeader.keterangan.label('keterangan')
    ).join(
        ItemPurchaseDetail,
        ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
    ).filter(
        func.lower(ItemPurchaseDetail.kodeitem) == kodeitem,
        ItemPurchaseHeader.tipe.in_(['BL', 'IM'])
    )

    # Get stock opname history
    opname_history = db.query(
        ItemStockOpname.tanggal,
        ItemStockOpname.periode,
        literal('OP').label('tipe'),
        ItemStockOpname.jmlselisih.label('jumlah'),
        ItemStockOpname.satuan,
        ItemStockOpname.harga,
        ItemStockOpname.total,
        literal(None).label('kodesupel'),
        literal('').label('keterangan')
    ).filter(
        func.lower(ItemStockOpname.kodeitem) == kodeitem.lower()
    )
    # Calculate monthly sales totals
    current_date = datetime.now()
    one_year_ago = current_date - timedelta(days=365)
    
    monthly_sales_data = db.query(
        extract('year', ItemSalesHeader.tanggal).label('year'),
        extract('month', ItemSalesHeader.tanggal).label('month'),
        func.sum(ItemSalesDetail.jumlah).label('total_units')
    ).join(
        ItemSalesDetail,
        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
    ).filter(
        func.lower(ItemSalesDetail.kodeitem) == kodeitem,
        ItemSalesHeader.tipe == 'KSR',  # Only count actual sales
        ItemSalesHeader.tanggal >= one_year_ago  # Only get last 12 months
    ).group_by(
        extract('year', ItemSalesHeader.tanggal),
        extract('month', ItemSalesHeader.tanggal)
    ).order_by(
        extract('year', ItemSalesHeader.tanggal).desc(),
        extract('month', ItemSalesHeader.tanggal).desc()
    ).all()  # Get all months within the last year
    
    # Format the monthly sales data
    monthly_sales = []
    for year, month, total_units in monthly_sales_data:
        month_name = calendar.month_name[int(month)]
        monthly_sales.append({
            'year': int(year),
            'month': int(month),
            'month_name': month_name,
            'units': int(total_units) if total_units else 0
        })
    # Get sales and purchase history combined
    sales_history = db.query(
        ItemSalesHeader.tanggal.label('tanggal'),
        ItemSalesHeader.notransaksi.label('notransaksi'),
        ItemSalesHeader.tipe.label('tipe'),
        ItemSalesDetail.jumlah.label('jumlah'),
        ItemSalesDetail.satuan.label('satuan'),
        ItemSalesDetail.harga.label('harga'),
        ItemSalesDetail.total.label('total'),
        ItemSalesHeader.kodesupel.label('kodesupel'),
        ItemSalesHeader.keterangan.label('keterangan')
    ).join(
        ItemSalesDetail,
        ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
    ).filter(
        func.lower(ItemSalesDetail.kodeitem) == kodeitem,
        ItemSalesHeader.tipe.in_(['KSR', 'RJ', 'IK', 'JL'])
    )

    purchase_history = db.query(
        ItemPurchaseHeader.tanggal.label('tanggal'),
        ItemPurchaseHeader.notransaksi.label('notransaksi'),
        ItemPurchaseHeader.tipe.label('tipe'),
        ItemPurchaseDetail.jumlah.label('jumlah'),
        ItemPurchaseDetail.satuan.label('satuan'),
        ItemPurchaseDetail.harga.label('harga'),
        ItemPurchaseDetail.total.label('total'),
        ItemPurchaseHeader.kodesupel.label('kodesupel'),
        ItemPurchaseHeader.keterangan.label('keterangan')
    ).join(
        ItemPurchaseDetail,
        ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
    ).filter(
        func.lower(ItemPurchaseDetail.kodeitem) == kodeitem,
        ItemPurchaseHeader.tipe.in_(['BL', 'IM'])
    )

    # Get stock opname history
    opname_history = db.query(
        ItemStockOpname.tanggal,
        ItemStockOpname.periode,
        literal('OP').label('tipe'),
        ItemStockOpname.jmlselisih.label('jumlah'),
        ItemStockOpname.satuan,
        ItemStockOpname.harga,
        ItemStockOpname.total,
        literal(None).label('kodesupel'),
        literal('').label('keterangan')
    ).filter(
        func.lower(ItemStockOpname.kodeitem) == kodeitem.lower()
    )

    # Combine all histories and sort
    combined_history = sales_history.union_all(purchase_history).union_all(opname_history)\
        .all()

    # Get unit conversions for the item
    unit_conversions = db.query(ItemUnitConversion)\
        .filter(func.lower(ItemUnitConversion.kodeitem) == kodeitem)\
        .all()

    # Remove suppliers query - we'll use kodesupel directly

    # Sort combined_history by date in ascending order for stock calculation
    combined_history = sorted(combined_history, key=lambda x: x.tanggal)
    
    # Calculate running stock balance starting from current stock
    history_data = []
    running_stock = current_stock
    
    # Process history records in reverse (newest to oldest)
    for record in reversed(combined_history):
        # First add the current record with current running_stock
        history_data.append({
            'tanggal': record.tanggal,
            'notransaksi': record.notransaksi,
            'tipe': record.tipe,
            'jumlah': record.jumlah,
            'satuan': record.satuan,
            'harga': record.harga,
            'total': record.total,
            'running_stock': running_stock,
            'supplier_name': record.kodesupel if hasattr(record, 'kodesupel') else '',
            'keterangan': record.keterangan if hasattr(record, 'keterangan') else ''
        })
        
        # Then calculate the next (previous) running_stock
        if record.tipe in ['KSR', 'IK', 'JL']:  # Sales/Outgoing
            stock_change = -record.jumlah  # Subtract sales quantity (will be added to previous balance)
        elif record.tipe in ['RJ', 'BL', 'IM']:  # Returns/Purchase/Incoming
            stock_change = record.jumlah  # Add returns/purchase quantity (will be subtracted from previous balance)
        elif record.tipe == 'OP':  # Stock Opname
            stock_change = record.jumlah  # Add/subtract opname difference
            
        # Convert to base unit if necessary
        for conv in unit_conversions:
            if conv.satuan == record.satuan and conv.tipe == 'K':
                stock_change *= conv.jumlahkonv
                break
                
        running_stock -= stock_change  # Calculate previous balance

    return templates.TemplateResponse(
        "item_history.html",
        {
            "request": request,
            "item": item,
            "current_stock": current_stock,
            "history": history_data,
            "monthly_sales": monthly_sales,
            "now": now,  # Pass the current date to the template
        }
    )

@router.get("/api/transaction-details/{notransaksi:path}")
async def get_transaction_details(notransaksi: str, db: Session = Depends(get_db)):
    try:
        # Query for sales transaction
        sales_details = db.query(
            ItemSalesDetail.kodeitem,
            Item.namaitem,
            ItemSalesDetail.jumlah,
            ItemSalesDetail.satuan,
            ItemSalesDetail.harga,
            ItemSalesDetail.total,
            ItemSalesDetail.potongan,
            ItemSalesHeader.tanggal,
            ItemSalesHeader.tipe,
            ItemSalesHeader.potfaktur,
            ItemSalesHeader.potnomfaktur,
            ItemSalesHeader.totalakhir,
        ).join(
            Item,
            Item.kodeitem == ItemSalesDetail.kodeitem
        ).join(
            ItemSalesHeader,
            ItemSalesHeader.notransaksi == ItemSalesDetail.notransaksi
        ).filter(
            ItemSalesDetail.notransaksi == notransaksi
        ).all()

        # Query for purchase transaction if no sales found
        purchase_details = None
        if not sales_details:
            purchase_details = db.query(
                ItemPurchaseDetail.kodeitem,
                Item.namaitem,
                ItemPurchaseDetail.jumlah,
                ItemPurchaseDetail.satuan,
                ItemPurchaseDetail.harga,
                ItemPurchaseDetail.total,
                ItemPurchaseDetail.potongan,
                ItemPurchaseHeader.tanggal,
                ItemPurchaseHeader.tipe,
                ItemPurchaseHeader.potfaktur,
                ItemPurchaseHeader.potnomfaktur,
                ItemPurchaseHeader.totalakhir,
            ).join(
                Item,
                Item.kodeitem == ItemPurchaseDetail.kodeitem
            ).join(
                ItemPurchaseHeader,
                ItemPurchaseHeader.notransaksi == ItemPurchaseDetail.notransaksi
            ).filter(
                ItemPurchaseDetail.notransaksi == notransaksi
            ).all()

        # Combine results
        details = sales_details or purchase_details
        if not details:
            raise HTTPException(status_code=404, detail=f"Transaction {notransaksi} not found")

        # Get the first record for header info
        first_record = details[0]
        
        return {
            "header": {
                "notransaksi": notransaksi,
                "tanggal": first_record.tanggal,
                "tipe": first_record.tipe,
                "potfaktur": float(first_record.potfaktur or 0),
                "potnomfaktur": float(first_record.potnomfaktur or 0),
                "totalakhir": float(first_record.totalakhir or 0)
            },
            "details": [
                {
                    "kodeitem": detail.kodeitem,
                    "namaitem": detail.namaitem,
                    "jumlah": float(detail.jumlah),
                    "satuan": detail.satuan,
                    "harga": float(detail.harga),
                    "total": float(detail.total),
                    "potongan": float(detail.potongan or 0)
                }
                for detail in details
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
