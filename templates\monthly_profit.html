<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Profit Report</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    backgroundColor: {
                        "gray-750": "#2d3748",
                        "gray-850": "#1a1a1a",
                        "gray-880": "#242424",
                        "gray-900": "#1e293b",
                    },
                },
            },
        };
    </script>
</head>
<body class="bg-black text-gray-100">
    <div class="min-h-screen">
        <!-- Navbar -->
        <nav class="bg-gray-800 shadow-lg sticky top-0 z-50">
            <div class="max-w-[95%] mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-3xl font-bold text-white">Monthly Profit Report</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button
                            onclick="window.close()"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-[95%] mx-auto px-4 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Current Year -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h2 class="text-2xl font-bold mb-4">Current Year ({{ current_year }})</h2>
                    <div class="space-y-4">
                        {% for month in current_months %}
                        <div class="flex flex-col p-3 bg-gray-700 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-lg">{{ month.name }}</span>
                                <div class="flex flex-col items-end">
                                    <span class="text-xl font-bold text-gray-300">{{ month.gross_sales | currency }}</span>
                                    <span class="text-lg font-bold text-green-400">{{ month.net_profit | currency }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Last Year -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h2 class="text-2xl font-bold mb-4">Last Year ({{ last_year }})</h2>
                    <div class="space-y-4">
                        {% for month in last_year_months %}
                        <div class="flex flex-col p-3 bg-gray-700 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-lg">{{ month.name }}</span>
                                <div class="flex flex-col items-end">
                                    <span class="text-xl font-bold text-gray-300">{{ month.gross_sales | currency }}</span>
                                    <span class="text-lg font-bold text-blue-400">{{ month.net_profit | currency }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

