<!DOCTYPE html>
<html>
  <head>
    <title>3-Month Sales Analysis</title>
    <link
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
      rel="stylesheet"
    />
  </head>
  <body class="bg-gray-900 text-white">
    <!-- Navigation Bar -->
    <nav class="bg-gray-800 shadow-lg">
      <div class="max-w-[95%] mx-auto px-4">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-3xl font-bold text-white">3-Month Sales</h1>
          </div>
          <div class="flex items-center">
            <button
              onclick="closeModal()"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
              Close
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="min-h-screen p-8" id="mainContent">
      <!-- Search Bar -->
      <div class="max-w-[95%] mx-auto mb-8">
        <form method="get" action="/three-month-sales" class="flex gap-2">
          <input
            type="text"
            id="searchBox"
            name="search"
            value="{{ search or '' }}"
            placeholder="Search items..."
            class="flex-1 px-4 py-2 text-lg rounded-lg border border-gray-600 bg-black text-white focus:ring-2 focus:ring-blue-500"
          />
          <input
            type="hidden"
            name="distinct"
            value="{{ 'true' if distinct else 'false' }}"
            id="distinctToggle"
          />
          <button
            type="submit"
            class="px-6 py-2 text-lg bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150"
          >
            Search
          </button>
          <button
            type="button"
            onclick="toggleDistinct()"
            class="px-6 py-2 text-lg {{ 'bg-green-600 hover:bg-green-700' if distinct else 'bg-gray-600 hover:bg-gray-700' }} text-white rounded-lg transition duration-150"
          >
            Distinct {{ '✓' if distinct else '✗' }}
          </button>
        </form>
        {% if search %}
        <div class="mt-2 text-gray-400">
          Row = {{ total_rows or 0 }}, Item = {{ total_items or 0 }}
        </div>
        {% endif %}
      </div>

      <script>
        function toggleDistinct() {
          const form = document.querySelector("form");
          const distinctToggle = document.getElementById("distinctToggle");
          distinctToggle.value =
            distinctToggle.value === "true" ? "false" : "true";
          form.submit();
        }
      </script>

      <!-- Table -->
      <div class="max-w-[95%] mx-auto overflow-x-auto">
        {% if not search %}
        <div class="text-center py-8 text-gray-400">
          <p class="text-xl">Enter a search term to find items</p>
          <p class="text-sm mt-2">
            Use the search box above to start searching
          </p>
        </div>
        {% else %}
        <table class="min-w-full divide-y divide-gray-700">
          <thead class="bg-gray-700">
            <tr>
              <th
                scope="col"
                class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                data-sort="kodeitem"
                onclick="sortTable('kodeitem')"
              >
                Kode Item
                <span class="sort-indicator opacity-0 ml-1">↑</span>
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                data-sort="namaitem"
                onclick="sortTable('namaitem')"
              >
                Nama Item
                <span class="sort-indicator opacity-0 ml-1">↑</span>
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                data-sort="stok"
                onclick="sortTable('stok')"
              >
                Stok
                <span class="sort-indicator opacity-0 ml-1">↑</span>
              </th>
              <th
                scope="col"
                class="px-4 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-600"
                data-sort="needed"
                onclick="sortTable('needed')"
              >
                Needed
                <span class="sort-indicator opacity-0 ml-1">↑</span>
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Last 3M Sales
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Merek
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Jenis
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                HPP
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Harga Jual
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Rak
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Keterangan
              </th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Supplier
              </th>
            </tr>
          </thead>
          <tbody class="bg-gray-800 divide-y divide-gray-700">
            {% for item_data in items %}
            <tr class="hover:bg-gray-700 transition duration-150">
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                <a
                  href="/item-history/{{ item_data.item.kodeitem }}"
                  target="_blank"
                  class="hover:text-blue-400 transition-colors duration-150"
                >
                  {{ item_data.item.kodeitem }}
                </a>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                <a
                  href="/edit-item/{{ item_data.item.kodeitem }}"
                  target="_blank"
                  class="hover:text-blue-400 transition-colors duration-150"
                >
                  {{ item_data.item.namaitem }}
                </a>
              </td>
              <td
                class="px-4 py-3 whitespace-nowrap text-sm {% if item_data.is_low_stock %}text-red-400{% else %}text-gray-300{% endif %}"
              >
                {{ "%.2f"|format(item_data.stok) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ "%.2f"|format(item_data.stock_needed_3month) if
                item_data.stock_needed_3month else '-' }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ item_data.last_3months_sales if item_data.last_3months_sales
                else '-' }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ item_data.item.merek }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ item_data.item.jenis }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ "{:,.0f}".format(item_data.hargapokok) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ "{:,.0f}".format(item_data.hargajual) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ item_data.item.rak }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ item_data.item.keterangan }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                {{ item_data.item.supplier1 }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
        {% endif %}
      </div>
    </div>

    <script>
      // Function to safely call parent window functions
      function closeModal() {
        try {
          // Try to call the parent's closeThreeMonthModal function
          if (
            window.parent &&
            typeof window.parent.closeThreeMonthModal === "function"
          ) {
            window.parent.closeThreeMonthModal();
          } else {
            // Fallback to window.close() if we're not in an iframe
            window.close();
          }
        } catch (e) {
          console.error("Error closing modal:", e);
          // Last resort fallback
          window.close();
        }
      }

      document.addEventListener("DOMContentLoaded", function () {
        const searchBox = document.getElementById("searchBox");
        const mainContent = document.getElementById("mainContent");

        // Function to focus and select search box text
        function focusAndSelectSearch() {
          searchBox.focus();
          searchBox.select();
        }

        // Focus and select search box text on page load
        focusAndSelectSearch();

        // Add click event listener to the main content
        mainContent.addEventListener("click", function (event) {
          // Check if clicked element is not an interactive element
          if (
            !event.target.matches(
              'button, input, a, select, textarea, [role="button"]'
            )
          ) {
            focusAndSelectSearch();
          }
        });

        // Focus and select after form submission and page load
        if (
          window.performance &&
          window.performance.navigation.type ===
            window.performance.navigation.TYPE_NAVIGATE
        ) {
          focusAndSelectSearch();
        }
      });

      // Add copy on select functionality
      document.addEventListener("mouseup", function () {
        const selectedText = window.getSelection().toString().trim();
        if (selectedText) {
          navigator.clipboard.writeText(selectedText).catch((err) => {
            console.error("Failed to copy text: ", err);
          });
        }
      });

      let sortDirection = {
        kodeitem: "asc",
        namaitem: "asc",
        stok: "asc",
        needed: "asc",
        merek: "asc",
        jenis: "asc",
        harga: "asc",
        rak: "asc",
        keterangan: "asc",
        supplier: "asc",
      };

      function sortTable(column) {
        console.log(
          `Sorting by ${column}, current direction: ${sortDirection[column]}`
        );

        const table = document.querySelector("table");
        const tbody = table.querySelector("tbody");
        const rows = Array.from(tbody.querySelectorAll("tr"));

        // Toggle sort direction
        sortDirection[column] =
          sortDirection[column] === "asc" ? "desc" : "asc";
        console.log(`New direction: ${sortDirection[column]}`);

        // Define column indices mapping
        const columnMap = {
          kodeitem: 0,
          namaitem: 1,
          stok: 2,
          needed: 3,
          merek: 4,
          jenis: 5,
          harga: 6,
          rak: 7,
          keterangan: 8,
          supplier: 9,
        };

        rows.sort((a, b) => {
          let aValue = a
            .querySelector(`td:nth-child(${columnMap[column] + 1})`)
            .textContent.trim();
          let bValue = b
            .querySelector(`td:nth-child(${columnMap[column] + 1})`)
            .textContent.trim();

          // Convert to numbers for numerical columns
          if (column === "stok" || column === "needed" || column === "harga") {
            // Remove currency formatting and convert to number
            aValue = parseFloat(aValue.replace(/[^0-9.-]+/g, "")) || 0;
            bValue = parseFloat(bValue.replace(/[^0-9.-]+/g, "")) || 0;
            return sortDirection[column] === "asc"
              ? aValue - bValue
              : bValue - aValue;
          }

          // String comparison for text columns
          return sortDirection[column] === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        });

        // Clear and repopulate tbody
        rows.forEach((row) => tbody.appendChild(row));

        // Update sort indicators
        updateSortIndicators(column);

        // After sorting
        console.log(
          `Table sorted by ${column} in ${sortDirection[column]} order`
        );
      }

      function updateSortIndicators(activeColumn) {
        const headers = document.querySelectorAll("th[data-sort]");
        headers.forEach((header) => {
          const column = header.getAttribute("data-sort");
          const indicator = header.querySelector(".sort-indicator");

          if (column === activeColumn) {
            indicator.textContent = sortDirection[column] === "asc" ? "↑" : "↓";
            indicator.classList.remove("opacity-0");
          } else {
            indicator.classList.add("opacity-0");
          }
        });
      }

      // Sort by kodeitem descending by default when page loads
      document.addEventListener("DOMContentLoaded", function () {
        // Only sort if there are items in the table
        if (document.querySelector("tbody tr")) {
          // First set direction to asc
          sortDirection["kodeitem"] = "desc";

          // Call sortTable which will toggle it to desc
          sortTable("kodeitem");

          console.log("Default sort applied: kodeitem descending");
          console.log("Current sort direction:", sortDirection["kodeitem"]);
        }
      });
    </script>
  </body>
</html>
